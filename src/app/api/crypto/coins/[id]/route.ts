import { NextRequest, NextResponse } from 'next/server';

const COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const { id } = params;

    // Extract query parameters
    const localization = searchParams.get('localization') || 'false';
    const tickers = searchParams.get('tickers') || 'false';
    const market_data = searchParams.get('market_data') || 'true';
    const community_data = searchParams.get('community_data') || 'true';
    const developer_data = searchParams.get('developer_data') || 'true';
    const sparkline = searchParams.get('sparkline') || 'false';

    // Build the CoinGecko API URL
    const coinGeckoUrl = new URL(`${COINGECKO_API_BASE}/coins/${id}`);
    coinGeckoUrl.searchParams.set('localization', localization);
    coinGeckoUrl.searchParams.set('tickers', tickers);
    coinGeckoUrl.searchParams.set('market_data', market_data);
    coinGeckoUrl.searchParams.set('community_data', community_data);
    coinGeckoUrl.searchParams.set('developer_data', developer_data);
    coinGeckoUrl.searchParams.set('sparkline', sparkline);

    // Make the request to CoinGecko
    const response = await fetch(coinGeckoUrl.toString(), {
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`CoinGecko API error: ${response.status}`);
    }

    const data = await response.json();

    // Return the data with CORS headers
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error(`Error fetching coin details for ${params.id}:`, error);
    return NextResponse.json(
      { error: `Failed to fetch details for ${params.id}` },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
