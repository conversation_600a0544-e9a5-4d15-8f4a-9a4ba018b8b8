import { NextRequest, NextResponse } from 'next/server';

const COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract query parameters
    const vs_currency = searchParams.get('vs_currency') || 'usd';
    const order = searchParams.get('order') || 'market_cap_desc';
    const per_page = searchParams.get('per_page') || '100';
    const page = searchParams.get('page') || '1';
    const sparkline = searchParams.get('sparkline') || 'true';
    const price_change_percentage = searchParams.get('price_change_percentage') || '1h,24h,7d,30d';

    // Build the CoinGecko API URL
    const coinGeckoUrl = new URL(`${COINGECKO_API_BASE}/coins/markets`);
    coinGeckoUrl.searchParams.set('vs_currency', vs_currency);
    coinGeckoUrl.searchParams.set('order', order);
    coinGeckoUrl.searchParams.set('per_page', per_page);
    coinGeckoUrl.searchParams.set('page', page);
    coinGeckoUrl.searchParams.set('sparkline', sparkline);
    coinGeckoUrl.searchParams.set('price_change_percentage', price_change_percentage);

    // Make the request to CoinGecko
    const response = await fetch(coinGeckoUrl.toString(), {
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`CoinGecko API error: ${response.status}`);
    }

    const data = await response.json();

    // Return the data with CORS headers
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Error fetching cryptocurrency data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cryptocurrency data' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
