{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface ErrorDisplayProps {\n  error?: Error | null;\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport default function ErrorDisplay({ \n  error, \n  onRetry, \n  className = '' \n}: ErrorDisplayProps) {\n  return (\n    <div className={`flex flex-col items-center justify-center p-8 text-center ${className}`}>\n      <div className=\"w-16 h-16 mb-4 text-red-500\">\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n          strokeWidth={1.5}\n          stroke=\"currentColor\"\n          className=\"w-full h-full\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            d=\"M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z\"\n          />\n        </svg>\n      </div>\n      \n      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n        Something went wrong\n      </h3>\n      \n      <p className=\"text-gray-600 dark:text-gray-400 mb-4 max-w-md\">\n        {error?.message || 'An unexpected error occurred while loading the data.'}\n      </p>\n      \n      {onRetry && (\n        <button\n          onClick={onRetry}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          Try Again\n        </button>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAUe,SAAS,aAAa,EACnC,KAAK,EACL,OAAO,EACP,YAAY,EAAE,EACI;IAClB,qBACE,8OAAC;QAAI,WAAW,CAAC,0DAA0D,EAAE,WAAW;;0BACtF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,aAAa;oBACb,QAAO;oBACP,WAAU;8BAEV,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,GAAE;;;;;;;;;;;;;;;;0BAKR,8OAAC;gBAAG,WAAU;0BAA2D;;;;;;0BAIzE,8OAAC;gBAAE,WAAU;0BACV,OAAO,WAAW;;;;;;YAGpB,yBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { CryptoCurrency, CoinDetails, HistoricalData } from '@/types/crypto';\n\n// Use local API routes to avoid CORS issues\nconst API_BASE = '/api/crypto';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE,\n  timeout: 10000,\n  headers: {\n    'Accept': 'application/json',\n  },\n});\n\n// Add request interceptor for rate limiting\napi.interceptors.request.use((config) => {\n  // Add a small delay to respect rate limits\n  return new Promise((resolve) => {\n    setTimeout(() => resolve(config), 100);\n  });\n});\n\nexport class CryptoAPI {\n  /**\n   * Fetch top cryptocurrencies by market cap\n   */\n  static async getTopCryptocurrencies(\n    limit: number = 100,\n    page: number = 1,\n    currency: string = 'usd'\n  ): Promise<CryptoCurrency[]> {\n    try {\n      const response = await api.get('/markets', {\n        params: {\n          vs_currency: currency,\n          order: 'market_cap_desc',\n          per_page: limit,\n          page: page,\n          sparkline: true,\n          price_change_percentage: '1h,24h,7d,30d',\n        },\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching top cryptocurrencies:', error);\n      throw new Error('Failed to fetch cryptocurrency data');\n    }\n  }\n\n  /**\n   * Fetch detailed information about a specific cryptocurrency\n   */\n  static async getCoinDetails(coinId: string): Promise<CoinDetails> {\n    try {\n      const response = await api.get(`/coins/${coinId}`, {\n        params: {\n          localization: false,\n          tickers: false,\n          market_data: true,\n          community_data: true,\n          developer_data: true,\n          sparkline: false,\n        },\n      });\n      return response.data;\n    } catch (error) {\n      console.error(`Error fetching details for ${coinId}:`, error);\n      throw new Error(`Failed to fetch details for ${coinId}`);\n    }\n  }\n\n  /**\n   * Fetch historical price data for a cryptocurrency\n   */\n  static async getHistoricalData(\n    coinId: string,\n    days: number = 30,\n    currency: string = 'usd'\n  ): Promise<HistoricalData> {\n    try {\n      const response = await api.get(`/coins/${coinId}/market_chart`, {\n        params: {\n          vs_currency: currency,\n          days: days,\n          interval: days <= 1 ? 'hourly' : days <= 90 ? 'daily' : 'weekly',\n        },\n      });\n      return response.data;\n    } catch (error) {\n      console.error(`Error fetching historical data for ${coinId}:`, error);\n      throw new Error(`Failed to fetch historical data for ${coinId}`);\n    }\n  }\n\n  /**\n   * Search for cryptocurrencies by name or symbol\n   */\n  static async searchCryptocurrencies(query: string): Promise<any[]> {\n    try {\n      const response = await api.get('/search', {\n        params: {\n          query: query,\n        },\n      });\n      return response.data.coins || [];\n    } catch (error) {\n      console.error('Error searching cryptocurrencies:', error);\n      throw new Error('Failed to search cryptocurrencies');\n    }\n  }\n\n  /**\n   * Fetch global cryptocurrency market data\n   */\n  static async getGlobalData(): Promise<any> {\n    try {\n      const response = await api.get('/global');\n      return response.data.data;\n    } catch (error) {\n      console.error('Error fetching global data:', error);\n      throw new Error('Failed to fetch global market data');\n    }\n  }\n\n  /**\n   * Fetch trending cryptocurrencies\n   */\n  static async getTrendingCryptocurrencies(): Promise<any[]> {\n    try {\n      const response = await api.get('/search/trending');\n      return response.data.coins || [];\n    } catch (error) {\n      console.error('Error fetching trending cryptocurrencies:', error);\n      throw new Error('Failed to fetch trending cryptocurrencies');\n    }\n  }\n}\n\nexport default CryptoAPI;\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,4CAA4C;AAC5C,MAAM,WAAW;AAEjB,4CAA4C;AAC5C,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;IACT,SAAS;QACP,UAAU;IACZ;AACF;AAEA,4CAA4C;AAC5C,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC5B,2CAA2C;IAC3C,OAAO,IAAI,QAAQ,CAAC;QAClB,WAAW,IAAM,QAAQ,SAAS;IACpC;AACF;AAEO,MAAM;IACX;;GAEC,GACD,aAAa,uBACX,QAAgB,GAAG,EACnB,OAAe,CAAC,EAChB,WAAmB,KAAK,EACG;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,YAAY;gBACzC,QAAQ;oBACN,aAAa;oBACb,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,WAAW;oBACX,yBAAyB;gBAC3B;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,eAAe,MAAc,EAAwB;QAChE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;gBACjD,QAAQ;oBACN,cAAc;oBACd,SAAS;oBACT,aAAa;oBACb,gBAAgB;oBAChB,gBAAgB;oBAChB,WAAW;gBACb;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC,EAAE;YACvD,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,QAAQ;QACzD;IACF;IAEA;;GAEC,GACD,aAAa,kBACX,MAAc,EACd,OAAe,EAAE,EACjB,WAAmB,KAAK,EACC;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,aAAa,CAAC,EAAE;gBAC9D,QAAQ;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU,QAAQ,IAAI,WAAW,QAAQ,KAAK,UAAU;gBAC1D;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAC/D,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,QAAQ;QACjE;IACF;IAEA;;GAEC,GACD,aAAa,uBAAuB,KAAa,EAAkB;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;gBACxC,QAAQ;oBACN,OAAO;gBACT;YACF;YACA,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,gBAA8B;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,8BAA8C;QACzD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM,IAAI,MAAM;QAClB;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/utils/dataTransform.ts"], "sourcesContent": ["import { CryptoCurrency, BubbleData, FilterOptions } from '@/types/crypto';\n\n/**\n * Transform cryptocurrency data into bubble chart format\n */\nexport function transformToBubbleData(cryptos: CryptoCurrency[]): BubbleData[] {\n  return cryptos.map((crypto) => ({\n    id: crypto.id,\n    symbol: crypto.symbol.toUpperCase(),\n    name: crypto.name,\n    value: crypto.market_cap || 0,\n    change: crypto.price_change_percentage_24h || 0,\n    price: crypto.current_price || 0,\n    volume: crypto.total_volume || 0,\n    rank: crypto.market_cap_rank || 0,\n    image: crypto.image,\n  }));\n}\n\n/**\n * Calculate bubble radius based on market cap\n */\nexport function calculateBubbleRadius(\n  marketCap: number,\n  minRadius: number = 10,\n  maxRadius: number = 100,\n  allMarketCaps: number[] = []\n): number {\n  if (allMarketCaps.length === 0) {\n    return minRadius;\n  }\n\n  const minMarketCap = Math.min(...allMarketCaps);\n  const maxMarketCap = Math.max(...allMarketCaps);\n\n  if (maxMarketCap === minMarketCap) {\n    return minRadius;\n  }\n\n  // Use square root scale for better visual distribution\n  const normalizedValue = Math.sqrt(marketCap - minMarketCap) / Math.sqrt(maxMarketCap - minMarketCap);\n  return minRadius + (maxRadius - minRadius) * normalizedValue;\n}\n\n/**\n * Get color based on price change percentage\n */\nexport function getPriceChangeColor(change: number): string {\n  if (change > 0) {\n    // Green shades for positive changes\n    if (change > 10) return '#00C851'; // Strong green\n    if (change > 5) return '#00FF41'; // Medium green\n    return '#4CAF50'; // Light green\n  } else if (change < 0) {\n    // Red shades for negative changes\n    if (change < -10) return '#FF1744'; // Strong red\n    if (change < -5) return '#FF5722'; // Medium red\n    return '#F44336'; // Light red\n  }\n  return '#9E9E9E'; // Gray for no change\n}\n\n/**\n * Filter cryptocurrencies based on criteria\n */\nexport function filterCryptocurrencies(\n  cryptos: CryptoCurrency[],\n  filters: FilterOptions\n): CryptoCurrency[] {\n  return cryptos.filter((crypto) => {\n    // Market cap filter\n    if (filters.minMarketCap && crypto.market_cap < filters.minMarketCap) {\n      return false;\n    }\n    if (filters.maxMarketCap && crypto.market_cap > filters.maxMarketCap) {\n      return false;\n    }\n\n    // Volume filter\n    if (filters.minVolume && crypto.total_volume < filters.minVolume) {\n      return false;\n    }\n    if (filters.maxVolume && crypto.total_volume > filters.maxVolume) {\n      return false;\n    }\n\n    // Price change filter\n    if (filters.minPriceChange && crypto.price_change_percentage_24h < filters.minPriceChange) {\n      return false;\n    }\n    if (filters.maxPriceChange && crypto.price_change_percentage_24h > filters.maxPriceChange) {\n      return false;\n    }\n\n    // Search query filter\n    if (filters.searchQuery) {\n      const query = filters.searchQuery.toLowerCase();\n      const nameMatch = crypto.name.toLowerCase().includes(query);\n      const symbolMatch = crypto.symbol.toLowerCase().includes(query);\n      if (!nameMatch && !symbolMatch) {\n        return false;\n      }\n    }\n\n    return true;\n  });\n}\n\n/**\n * Sort cryptocurrencies by various criteria\n */\nexport function sortCryptocurrencies(\n  cryptos: CryptoCurrency[],\n  sortBy: 'market_cap' | 'price' | 'volume' | 'change_24h' | 'name',\n  order: 'asc' | 'desc' = 'desc'\n): CryptoCurrency[] {\n  return [...cryptos].sort((a, b) => {\n    let aValue: number | string;\n    let bValue: number | string;\n\n    switch (sortBy) {\n      case 'market_cap':\n        aValue = a.market_cap || 0;\n        bValue = b.market_cap || 0;\n        break;\n      case 'price':\n        aValue = a.current_price || 0;\n        bValue = b.current_price || 0;\n        break;\n      case 'volume':\n        aValue = a.total_volume || 0;\n        bValue = b.total_volume || 0;\n        break;\n      case 'change_24h':\n        aValue = a.price_change_percentage_24h || 0;\n        bValue = b.price_change_percentage_24h || 0;\n        break;\n      case 'name':\n        aValue = a.name.toLowerCase();\n        bValue = b.name.toLowerCase();\n        break;\n      default:\n        aValue = a.market_cap || 0;\n        bValue = b.market_cap || 0;\n    }\n\n    if (typeof aValue === 'string' && typeof bValue === 'string') {\n      return order === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);\n    }\n\n    const numA = aValue as number;\n    const numB = bValue as number;\n    return order === 'asc' ? numA - numB : numB - numA;\n  });\n}\n\n/**\n * Format currency values\n */\nexport function formatCurrency(value: number, currency: string = 'USD'): string {\n  if (value === 0) return '$0';\n  \n  if (value >= 1e12) {\n    return `$${(value / 1e12).toFixed(2)}T`;\n  }\n  if (value >= 1e9) {\n    return `$${(value / 1e9).toFixed(2)}B`;\n  }\n  if (value >= 1e6) {\n    return `$${(value / 1e6).toFixed(2)}M`;\n  }\n  if (value >= 1e3) {\n    return `$${(value / 1e3).toFixed(2)}K`;\n  }\n  \n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: value < 1 ? 4 : 2,\n    maximumFractionDigits: value < 1 ? 4 : 2,\n  }).format(value);\n}\n\n/**\n * Format percentage values\n */\nexport function formatPercentage(value: number): string {\n  if (value === 0) return '0.00%';\n  \n  return new Intl.NumberFormat('en-US', {\n    style: 'percent',\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(value / 100);\n}\n\n/**\n * Calculate portfolio statistics\n */\nexport function calculatePortfolioStats(holdings: any[]): {\n  totalValue: number;\n  totalChange24h: number;\n  totalChangePercentage24h: number;\n  bestPerformer: any | null;\n  worstPerformer: any | null;\n} {\n  if (holdings.length === 0) {\n    return {\n      totalValue: 0,\n      totalChange24h: 0,\n      totalChangePercentage24h: 0,\n      bestPerformer: null,\n      worstPerformer: null,\n    };\n  }\n\n  const totalValue = holdings.reduce((sum, holding) => sum + holding.value, 0);\n  const totalChange24h = holdings.reduce((sum, holding) => sum + holding.change24h, 0);\n  const totalChangePercentage24h = totalValue > 0 ? (totalChange24h / (totalValue - totalChange24h)) * 100 : 0;\n\n  const sortedByPerformance = [...holdings].sort((a, b) => b.changePercentage24h - a.changePercentage24h);\n  const bestPerformer = sortedByPerformance[0];\n  const worstPerformer = sortedByPerformance[sortedByPerformance.length - 1];\n\n  return {\n    totalValue,\n    totalChange24h,\n    totalChangePercentage24h,\n    bestPerformer,\n    worstPerformer,\n  };\n}\n\n/**\n * Debounce function for search inputs\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAKO,SAAS,sBAAsB,OAAyB;IAC7D,OAAO,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;YAC9B,IAAI,OAAO,EAAE;YACb,QAAQ,OAAO,MAAM,CAAC,WAAW;YACjC,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,UAAU,IAAI;YAC5B,QAAQ,OAAO,2BAA2B,IAAI;YAC9C,OAAO,OAAO,aAAa,IAAI;YAC/B,QAAQ,OAAO,YAAY,IAAI;YAC/B,MAAM,OAAO,eAAe,IAAI;YAChC,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAKO,SAAS,sBACd,SAAiB,EACjB,YAAoB,EAAE,EACtB,YAAoB,GAAG,EACvB,gBAA0B,EAAE;IAE5B,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,eAAe,KAAK,GAAG,IAAI;IACjC,MAAM,eAAe,KAAK,GAAG,IAAI;IAEjC,IAAI,iBAAiB,cAAc;QACjC,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,kBAAkB,KAAK,IAAI,CAAC,YAAY,gBAAgB,KAAK,IAAI,CAAC,eAAe;IACvF,OAAO,YAAY,CAAC,YAAY,SAAS,IAAI;AAC/C;AAKO,SAAS,oBAAoB,MAAc;IAChD,IAAI,SAAS,GAAG;QACd,oCAAoC;QACpC,IAAI,SAAS,IAAI,OAAO,WAAW,eAAe;QAClD,IAAI,SAAS,GAAG,OAAO,WAAW,eAAe;QACjD,OAAO,WAAW,cAAc;IAClC,OAAO,IAAI,SAAS,GAAG;QACrB,kCAAkC;QAClC,IAAI,SAAS,CAAC,IAAI,OAAO,WAAW,aAAa;QACjD,IAAI,SAAS,CAAC,GAAG,OAAO,WAAW,aAAa;QAChD,OAAO,WAAW,YAAY;IAChC;IACA,OAAO,WAAW,qBAAqB;AACzC;AAKO,SAAS,uBACd,OAAyB,EACzB,OAAsB;IAEtB,OAAO,QAAQ,MAAM,CAAC,CAAC;QACrB,oBAAoB;QACpB,IAAI,QAAQ,YAAY,IAAI,OAAO,UAAU,GAAG,QAAQ,YAAY,EAAE;YACpE,OAAO;QACT;QACA,IAAI,QAAQ,YAAY,IAAI,OAAO,UAAU,GAAG,QAAQ,YAAY,EAAE;YACpE,OAAO;QACT;QAEA,gBAAgB;QAChB,IAAI,QAAQ,SAAS,IAAI,OAAO,YAAY,GAAG,QAAQ,SAAS,EAAE;YAChE,OAAO;QACT;QACA,IAAI,QAAQ,SAAS,IAAI,OAAO,YAAY,GAAG,QAAQ,SAAS,EAAE;YAChE,OAAO;QACT;QAEA,sBAAsB;QACtB,IAAI,QAAQ,cAAc,IAAI,OAAO,2BAA2B,GAAG,QAAQ,cAAc,EAAE;YACzF,OAAO;QACT;QACA,IAAI,QAAQ,cAAc,IAAI,OAAO,2BAA2B,GAAG,QAAQ,cAAc,EAAE;YACzF,OAAO;QACT;QAEA,sBAAsB;QACtB,IAAI,QAAQ,WAAW,EAAE;YACvB,MAAM,QAAQ,QAAQ,WAAW,CAAC,WAAW;YAC7C,MAAM,YAAY,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;YACrD,MAAM,cAAc,OAAO,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC;YACzD,IAAI,CAAC,aAAa,CAAC,aAAa;gBAC9B,OAAO;YACT;QACF;QAEA,OAAO;IACT;AACF;AAKO,SAAS,qBACd,OAAyB,EACzB,MAAiE,EACjE,QAAwB,MAAM;IAE9B,OAAO;WAAI;KAAQ,CAAC,IAAI,CAAC,CAAC,GAAG;QAC3B,IAAI;QACJ,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,SAAS,EAAE,UAAU,IAAI;gBACzB,SAAS,EAAE,UAAU,IAAI;gBACzB;YACF,KAAK;gBACH,SAAS,EAAE,aAAa,IAAI;gBAC5B,SAAS,EAAE,aAAa,IAAI;gBAC5B;YACF,KAAK;gBACH,SAAS,EAAE,YAAY,IAAI;gBAC3B,SAAS,EAAE,YAAY,IAAI;gBAC3B;YACF,KAAK;gBACH,SAAS,EAAE,2BAA2B,IAAI;gBAC1C,SAAS,EAAE,2BAA2B,IAAI;gBAC1C;YACF,KAAK;gBACH,SAAS,EAAE,IAAI,CAAC,WAAW;gBAC3B,SAAS,EAAE,IAAI,CAAC,WAAW;gBAC3B;YACF;gBACE,SAAS,EAAE,UAAU,IAAI;gBACzB,SAAS,EAAE,UAAU,IAAI;QAC7B;QAEA,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;YAC5D,OAAO,UAAU,QAAQ,OAAO,aAAa,CAAC,UAAU,OAAO,aAAa,CAAC;QAC/E;QAEA,MAAM,OAAO;QACb,MAAM,OAAO;QACb,OAAO,UAAU,QAAQ,OAAO,OAAO,OAAO;IAChD;AACF;AAKO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,IAAI,UAAU,GAAG,OAAO;IAExB,IAAI,SAAS,MAAM;QACjB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACzC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC;IAEA,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB,QAAQ,IAAI,IAAI;QACvC,uBAAuB,QAAQ,IAAI,IAAI;IACzC,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,iBAAiB,KAAa;IAC5C,IAAI,UAAU,GAAG,OAAO;IAExB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,QAAQ;AACpB;AAKO,SAAS,wBAAwB,QAAe;IAOrD,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;YACL,YAAY;YACZ,gBAAgB;YAChB,0BAA0B;YAC1B,eAAe;YACf,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,KAAK,EAAE;IAC1E,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,SAAS,EAAE;IAClF,MAAM,2BAA2B,aAAa,IAAI,AAAC,iBAAiB,CAAC,aAAa,cAAc,IAAK,MAAM;IAE3G,MAAM,sBAAsB;WAAI;KAAS,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,mBAAmB,GAAG,EAAE,mBAAmB;IACtG,MAAM,gBAAgB,mBAAmB,CAAC,EAAE;IAC5C,MAAM,iBAAiB,mBAAmB,CAAC,oBAAoB,MAAM,GAAG,EAAE;IAE1E,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/hooks/useCrypto.ts"], "sourcesContent": ["import useSWR from 'swr';\nimport { CryptoCurrency, CoinDetails, HistoricalData, BubbleData } from '@/types/crypto';\nimport { CryptoAPI } from '@/lib/api';\nimport { transformToBubbleData } from '@/utils/dataTransform';\n\n/**\n * Hook to fetch top cryptocurrencies\n */\nexport function useCryptocurrencies(\n  limit: number = 100,\n  page: number = 1,\n  currency: string = 'usd'\n) {\n  const { data, error, isLoading, mutate } = useSWR(\n    ['cryptocurrencies', limit, page, currency],\n    () => CryptoAPI.getTopCryptocurrencies(limit, page, currency),\n    {\n      refreshInterval: 30000, // Refresh every 30 seconds\n      revalidateOnFocus: true,\n      dedupingInterval: 10000, // Dedupe requests within 10 seconds\n    }\n  );\n\n  return {\n    cryptocurrencies: data || [],\n    isLoading,\n    error,\n    mutate,\n  };\n}\n\n/**\n * Hook to fetch bubble chart data\n */\nexport function useBubbleData(\n  limit: number = 100,\n  page: number = 1,\n  currency: string = 'usd'\n) {\n  const { cryptocurrencies, isLoading, error, mutate } = useCryptocurrencies(limit, page, currency);\n  \n  const bubbleData: BubbleData[] = cryptocurrencies ? transformToBubbleData(cryptocurrencies) : [];\n\n  return {\n    bubbleData,\n    isLoading,\n    error,\n    mutate,\n  };\n}\n\n/**\n * Hook to fetch coin details\n */\nexport function useCoinDetails(coinId: string | null) {\n  const { data, error, isLoading } = useSWR(\n    coinId ? ['coin-details', coinId] : null,\n    () => coinId ? CryptoAPI.getCoinDetails(coinId) : null,\n    {\n      revalidateOnFocus: false,\n      dedupingInterval: 60000, // Cache for 1 minute\n    }\n  );\n\n  return {\n    coinDetails: data,\n    isLoading,\n    error,\n  };\n}\n\n/**\n * Hook to fetch historical data\n */\nexport function useHistoricalData(\n  coinId: string | null,\n  days: number = 30,\n  currency: string = 'usd'\n) {\n  const { data, error, isLoading } = useSWR(\n    coinId ? ['historical-data', coinId, days, currency] : null,\n    () => coinId ? CryptoAPI.getHistoricalData(coinId, days, currency) : null,\n    {\n      revalidateOnFocus: false,\n      dedupingInterval: 300000, // Cache for 5 minutes\n    }\n  );\n\n  return {\n    historicalData: data,\n    isLoading,\n    error,\n  };\n}\n\n/**\n * Hook to search cryptocurrencies\n */\nexport function useSearchCryptocurrencies(query: string) {\n  const { data, error, isLoading } = useSWR(\n    query.length > 2 ? ['search', query] : null,\n    () => query.length > 2 ? CryptoAPI.searchCryptocurrencies(query) : null,\n    {\n      revalidateOnFocus: false,\n      dedupingInterval: 30000,\n    }\n  );\n\n  return {\n    searchResults: data || [],\n    isLoading,\n    error,\n  };\n}\n\n/**\n * Hook to fetch global market data\n */\nexport function useGlobalData() {\n  const { data, error, isLoading } = useSWR(\n    'global-data',\n    CryptoAPI.getGlobalData,\n    {\n      refreshInterval: 60000, // Refresh every minute\n      revalidateOnFocus: true,\n    }\n  );\n\n  return {\n    globalData: data,\n    isLoading,\n    error,\n  };\n}\n\n/**\n * Hook to fetch trending cryptocurrencies\n */\nexport function useTrendingCryptocurrencies() {\n  const { data, error, isLoading } = useSWR(\n    'trending',\n    CryptoAPI.getTrendingCryptocurrencies,\n    {\n      refreshInterval: 300000, // Refresh every 5 minutes\n      revalidateOnFocus: true,\n    }\n  );\n\n  return {\n    trending: data || [],\n    isLoading,\n    error,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;AACA;;;;AAKO,SAAS,oBACd,QAAgB,GAAG,EACnB,OAAe,CAAC,EAChB,WAAmB,KAAK;IAExB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAM,AAAD,EAC9C;QAAC;QAAoB;QAAO;QAAM;KAAS,EAC3C,IAAM,iHAAA,CAAA,YAAS,CAAC,sBAAsB,CAAC,OAAO,MAAM,WACpD;QACE,iBAAiB;QACjB,mBAAmB;QACnB,kBAAkB;IACpB;IAGF,OAAO;QACL,kBAAkB,QAAQ,EAAE;QAC5B;QACA;QACA;IACF;AACF;AAKO,SAAS,cACd,QAAgB,GAAG,EACnB,OAAe,CAAC,EAChB,WAAmB,KAAK;IAExB,MAAM,EAAE,gBAAgB,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,oBAAoB,OAAO,MAAM;IAExF,MAAM,aAA2B,uCAAmB,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,EAAE,oBAAoB;IAE9F,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,eAAe,MAAqB;IAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAM,AAAD,EACtC,SAAS;QAAC;QAAgB;KAAO,GAAG,MACpC,IAAM,SAAS,iHAAA,CAAA,YAAS,CAAC,cAAc,CAAC,UAAU,MAClD;QACE,mBAAmB;QACnB,kBAAkB;IACpB;IAGF,OAAO;QACL,aAAa;QACb;QACA;IACF;AACF;AAKO,SAAS,kBACd,MAAqB,EACrB,OAAe,EAAE,EACjB,WAAmB,KAAK;IAExB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAM,AAAD,EACtC,SAAS;QAAC;QAAmB;QAAQ;QAAM;KAAS,GAAG,MACvD,IAAM,SAAS,iHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC,QAAQ,MAAM,YAAY,MACrE;QACE,mBAAmB;QACnB,kBAAkB;IACpB;IAGF,OAAO;QACL,gBAAgB;QAChB;QACA;IACF;AACF;AAKO,SAAS,0BAA0B,KAAa;IACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAM,AAAD,EACtC,MAAM,MAAM,GAAG,IAAI;QAAC;QAAU;KAAM,GAAG,MACvC,IAAM,MAAM,MAAM,GAAG,IAAI,iHAAA,CAAA,YAAS,CAAC,sBAAsB,CAAC,SAAS,MACnE;QACE,mBAAmB;QACnB,kBAAkB;IACpB;IAGF,OAAO;QACL,eAAe,QAAQ,EAAE;QACzB;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAM,AAAD,EACtC,eACA,iHAAA,CAAA,YAAS,CAAC,aAAa,EACvB;QACE,iBAAiB;QACjB,mBAAmB;IACrB;IAGF,OAAO;QACL,YAAY;QACZ;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAM,AAAD,EACtC,YACA,iHAAA,CAAA,YAAS,CAAC,2BAA2B,EACrC;QACE,iBAAiB;QACjB,mBAAmB;IACrB;IAGF,OAAO;QACL,UAAU,QAAQ,EAAE;QACpB;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/AdvancedSearch.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Search, X, TrendingUp, TrendingDown, Star } from 'lucide-react';\nimport { useSearchCryptocurrencies, useTrendingCryptocurrencies } from '@/hooks/useCrypto';\nimport { debounce, formatCurrency, formatPercentage } from '@/utils/dataTransform';\n\ninterface AdvancedSearchProps {\n  onCryptoSelect?: (crypto: any) => void;\n  onAddToWatchlist?: (crypto: any) => void;\n  placeholder?: string;\n  className?: string;\n}\n\nexport default function AdvancedSearch({ \n  onCryptoSelect, \n  onAddToWatchlist,\n  placeholder = \"Search cryptocurrencies...\",\n  className = \"\"\n}: AdvancedSearchProps) {\n  const [query, setQuery] = useState('');\n  const [isOpen, setIsOpen] = useState(false);\n  const [debouncedQuery, setDebouncedQuery] = useState('');\n  const [activeTab, setActiveTab] = useState<'search' | 'trending'>('search');\n  const inputRef = useRef<HTMLInputElement>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  const { searchResults, isLoading: searchLoading } = useSearchCryptocurrencies(debouncedQuery);\n  const { trending, isLoading: trendingLoading } = useTrendingCryptocurrencies();\n\n  // Debounce search query\n  const debouncedSetQuery = debounce((value: string) => {\n    setDebouncedQuery(value);\n  }, 300);\n\n  useEffect(() => {\n    debouncedSetQuery(query);\n  }, [query, debouncedSetQuery]);\n\n  // Handle click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        dropdownRef.current &&\n        !dropdownRef.current.contains(event.target as Node) &&\n        !inputRef.current?.contains(event.target as Node)\n      ) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setQuery(value);\n    setIsOpen(true);\n    setActiveTab('search');\n  };\n\n  const handleInputFocus = () => {\n    setIsOpen(true);\n    if (query.length === 0) {\n      setActiveTab('trending');\n    }\n  };\n\n  const handleCryptoSelect = (crypto: any) => {\n    setQuery('');\n    setDebouncedQuery('');\n    setIsOpen(false);\n    onCryptoSelect?.(crypto);\n  };\n\n  const handleAddToWatchlist = (crypto: any, event: React.MouseEvent) => {\n    event.stopPropagation();\n    onAddToWatchlist?.(crypto);\n  };\n\n  const clearSearch = () => {\n    setQuery('');\n    setDebouncedQuery('');\n    setIsOpen(false);\n    inputRef.current?.focus();\n  };\n\n  const renderSearchResults = () => {\n    if (searchLoading) {\n      return (\n        <div className=\"px-4 py-3 text-sm text-gray-500 dark:text-gray-400\">\n          Searching...\n        </div>\n      );\n    }\n\n    if (searchResults.length === 0 && debouncedQuery.length > 2) {\n      return (\n        <div className=\"px-4 py-3 text-sm text-gray-500 dark:text-gray-400\">\n          No cryptocurrencies found for \"{debouncedQuery}\"\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"py-1\">\n        {searchResults.slice(0, 8).map((crypto) => (\n          <div\n            key={crypto.id}\n            className=\"px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors\"\n            onClick={() => handleCryptoSelect(crypto)}\n          >\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\n                <img\n                  src={crypto.large || crypto.thumb}\n                  alt={crypto.name}\n                  className=\"w-8 h-8 rounded-full flex-shrink-0\"\n                />\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {crypto.name}\n                    </span>\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400 uppercase\">\n                      {crypto.symbol}\n                    </span>\n                  </div>\n                  {crypto.market_cap_rank && (\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      Rank #{crypto.market_cap_rank}\n                    </div>\n                  )}\n                </div>\n              </div>\n              <button\n                onClick={(e) => handleAddToWatchlist(crypto, e)}\n                className=\"p-1 hover:bg-gray-200 dark:hover:bg-gray-500 rounded transition-colors\"\n                title=\"Add to watchlist\"\n              >\n                <Star className=\"w-4 h-4 text-gray-400 hover:text-yellow-500\" />\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  const renderTrendingResults = () => {\n    if (trendingLoading) {\n      return (\n        <div className=\"px-4 py-3 text-sm text-gray-500 dark:text-gray-400\">\n          Loading trending...\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"py-1\">\n        <div className=\"px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n          Trending Now\n        </div>\n        {trending.slice(0, 6).map((item) => {\n          const crypto = item.item;\n          return (\n            <div\n              key={crypto.id}\n              className=\"px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors\"\n              onClick={() => handleCryptoSelect(crypto)}\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\n                  <img\n                    src={crypto.large || crypto.thumb}\n                    alt={crypto.name}\n                    className=\"w-8 h-8 rounded-full flex-shrink-0\"\n                  />\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                        {crypto.name}\n                      </span>\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400 uppercase\">\n                        {crypto.symbol}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                        Rank #{crypto.market_cap_rank}\n                      </span>\n                      <div className=\"flex items-center space-x-1\">\n                        <TrendingUp className=\"w-3 h-3 text-orange-500\" />\n                        <span className=\"text-xs text-orange-500 font-medium\">\n                          Trending\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <button\n                  onClick={(e) => handleAddToWatchlist(crypto, e)}\n                  className=\"p-1 hover:bg-gray-200 dark:hover:bg-gray-500 rounded transition-colors\"\n                  title=\"Add to watchlist\"\n                >\n                  <Star className=\"w-4 h-4 text-gray-400 hover:text-yellow-500\" />\n                </button>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  return (\n    <div className={`relative ${className}`}>\n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <Search className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={handleInputChange}\n          onFocus={handleInputFocus}\n          placeholder={placeholder}\n          className=\"block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        />\n        {query && (\n          <button\n            onClick={clearSearch}\n            className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n          >\n            <X className=\"h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\" />\n          </button>\n        )}\n      </div>\n\n      {/* Search Results Dropdown */}\n      {isOpen && (\n        <div\n          ref={dropdownRef}\n          className=\"absolute z-50 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg max-h-80 overflow-hidden\"\n        >\n          {/* Tabs */}\n          <div className=\"flex border-b border-gray-200 dark:border-gray-600\">\n            <button\n              onClick={() => setActiveTab('search')}\n              className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${\n                activeTab === 'search'\n                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n              }`}\n            >\n              Search Results\n            </button>\n            <button\n              onClick={() => setActiveTab('trending')}\n              className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${\n                activeTab === 'trending'\n                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n              }`}\n            >\n              Trending\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className=\"max-h-64 overflow-y-auto\">\n            {activeTab === 'search' ? renderSearchResults() : renderTrendingResults()}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAce,SAAS,eAAe,EACrC,cAAc,EACd,gBAAgB,EAChB,cAAc,4BAA4B,EAC1C,YAAY,EAAE,EACM;IACpB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAClE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,EAAE,aAAa,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD,EAAE;IAC9E,MAAM,EAAE,QAAQ,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAE3E,wBAAwB;IACxB,MAAM,oBAAoB,CAAA,GAAA,6HAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QAClC,kBAAkB;IACpB,GAAG;IAEH,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;IACpB,GAAG;QAAC;QAAO;KAAkB;IAE7B,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC1C,CAAC,SAAS,OAAO,EAAE,SAAS,MAAM,MAAM,GACxC;gBACA,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QACT,UAAU;QACV,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,UAAU;QACV,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,SAAS;QACT,kBAAkB;QAClB,UAAU;QACV,iBAAiB;IACnB;IAEA,MAAM,uBAAuB,CAAC,QAAa;QACzC,MAAM,eAAe;QACrB,mBAAmB;IACrB;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,kBAAkB;QAClB,UAAU;QACV,SAAS,OAAO,EAAE;IACpB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,eAAe;YACjB,qBACE,8OAAC;gBAAI,WAAU;0BAAqD;;;;;;QAIxE;QAEA,IAAI,cAAc,MAAM,KAAK,KAAK,eAAe,MAAM,GAAG,GAAG;YAC3D,qBACE,8OAAC;gBAAI,WAAU;;oBAAqD;oBAClC;oBAAe;;;;;;;QAGrD;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBAC9B,8OAAC;oBAEC,WAAU;oBACV,SAAS,IAAM,mBAAmB;8BAElC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAK,OAAO,KAAK,IAAI,OAAO,KAAK;wCACjC,KAAK,OAAO,IAAI;wCAChB,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,OAAO,IAAI;;;;;;kEAEd,8OAAC;wDAAK,WAAU;kEACb,OAAO,MAAM;;;;;;;;;;;;4CAGjB,OAAO,eAAe,kBACrB,8OAAC;gDAAI,WAAU;;oDAA2C;oDACjD,OAAO,eAAe;;;;;;;;;;;;;;;;;;;0CAKrC,8OAAC;gCACC,SAAS,CAAC,IAAM,qBAAqB,QAAQ;gCAC7C,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;mBAhCf,OAAO,EAAE;;;;;;;;;;IAuCxB;IAEA,MAAM,wBAAwB;QAC5B,IAAI,iBAAiB;YACnB,qBACE,8OAAC;gBAAI,WAAU;0BAAqD;;;;;;QAIxE;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAAyF;;;;;;gBAGvG,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;oBACzB,MAAM,SAAS,KAAK,IAAI;oBACxB,qBACE,8OAAC;wBAEC,WAAU;wBACV,SAAS,IAAM,mBAAmB;kCAElC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK,OAAO,KAAK,IAAI,OAAO,KAAK;4CACjC,KAAK,OAAO,IAAI;4CAChB,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,OAAO,IAAI;;;;;;sEAEd,8OAAC;4DAAK,WAAU;sEACb,OAAO,MAAM;;;;;;;;;;;;8DAGlB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;gEAA2C;gEAClD,OAAO,eAAe;;;;;;;sEAE/B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,8OAAC;oEAAK,WAAU;8EAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO9D,8OAAC;oCACC,SAAS,CAAC,IAAM,qBAAqB,QAAQ;oCAC7C,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;uBAtCf,OAAO,EAAE;;;;;gBA2CpB;;;;;;;IAGN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,aAAa;wBACb,WAAU;;;;;;oBAEX,uBACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAMlB,wBACC,8OAAC;gBACC,KAAK;gBACL,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,uDAAuD,EACjE,cAAc,WACV,qFACA,iFACJ;0CACH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,uDAAuD,EACjE,cAAc,aACV,qFACA,iFACJ;0CACH;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;kCACZ,cAAc,WAAW,wBAAwB;;;;;;;;;;;;;;;;;;AAM9D", "debugId": null}}, {"offset": {"line": 1085, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/SettingsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Settings, X, RotateCcw, Save } from 'lucide-react';\nimport { useSettings } from '@/contexts/SettingsContext';\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { SUPPORTED_CURRENCIES, TIME_PERIODS, VISUALIZATION_MODES } from '@/constants';\n\ninterface SettingsPanelProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function SettingsPanel({ isOpen, onClose }: SettingsPanelProps) {\n  const { settings, updateSettings, resetSettings } = useSettings();\n  const { theme, toggleTheme } = useTheme();\n  const [tempSettings, setTempSettings] = useState(settings);\n\n  React.useEffect(() => {\n    setTempSettings(settings);\n  }, [settings]);\n\n  const handleSave = () => {\n    updateSettings(tempSettings);\n    onClose();\n  };\n\n  const handleReset = () => {\n    resetSettings();\n    setTempSettings(settings);\n  };\n\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div \n      className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\"\n      onClick={handleBackdropClick}\n    >\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center space-x-2\">\n            <Settings className=\"w-5 h-5 text-gray-500\" />\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n              Settings\n            </h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n          >\n            <X className=\"w-5 h-5 text-gray-500\" />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 space-y-6\">\n          {/* Appearance */}\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Appearance\n            </h3>\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Theme\n                </label>\n                <button\n                  onClick={toggleTheme}\n                  className=\"w-full px-4 py-2 text-left border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  {theme === 'dark' ? 'Dark Mode' : 'Light Mode'}\n                </button>\n              </div>\n\n              <div>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={tempSettings.enableAnimations}\n                    onChange={(e) => setTempSettings({ ...tempSettings, enableAnimations: e.target.checked })}\n                    className=\"mr-2 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    Enable animations\n                  </span>\n                </label>\n              </div>\n\n              <div>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={tempSettings.showLabels}\n                    onChange={(e) => setTempSettings({ ...tempSettings, showLabels: e.target.checked })}\n                    className=\"mr-2 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    Show labels on charts\n                  </span>\n                </label>\n              </div>\n\n              <div>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={tempSettings.showTooltips}\n                    onChange={(e) => setTempSettings({ ...tempSettings, showTooltips: e.target.checked })}\n                    className=\"mr-2 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    Show tooltips\n                  </span>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          {/* Data & Display */}\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Data & Display\n            </h3>\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Currency\n                </label>\n                <select\n                  value={tempSettings.currency}\n                  onChange={(e) => setTempSettings({ ...tempSettings, currency: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  {SUPPORTED_CURRENCIES.map((currency) => (\n                    <option key={currency.code} value={currency.code}>\n                      {currency.name} ({currency.symbol})\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Items per page\n                </label>\n                <select\n                  value={tempSettings.itemsPerPage}\n                  onChange={(e) => setTempSettings({ ...tempSettings, itemsPerPage: parseInt(e.target.value) })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value={50}>50</option>\n                  <option value={100}>100</option>\n                  <option value={200}>200</option>\n                  <option value={500}>500</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Default visualization\n                </label>\n                <select\n                  value={tempSettings.visualizationMode}\n                  onChange={(e) => setTempSettings({ ...tempSettings, visualizationMode: e.target.value as any })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  {VISUALIZATION_MODES.map((mode) => (\n                    <option key={mode.id} value={mode.id} disabled={mode.id !== 'bubble' && mode.id !== 'treemap'}>\n                      {mode.name} {mode.id !== 'bubble' && mode.id !== 'treemap' ? '(Coming Soon)' : ''}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Auto-refresh */}\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Auto-refresh\n            </h3>\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={tempSettings.autoRefresh}\n                    onChange={(e) => setTempSettings({ ...tempSettings, autoRefresh: e.target.checked })}\n                    className=\"mr-2 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    Enable auto-refresh\n                  </span>\n                </label>\n              </div>\n\n              {tempSettings.autoRefresh && (\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Refresh interval (seconds)\n                  </label>\n                  <select\n                    value={tempSettings.refreshInterval / 1000}\n                    onChange={(e) => setTempSettings({ ...tempSettings, refreshInterval: parseInt(e.target.value) * 1000 })}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value={10}>10 seconds</option>\n                    <option value={30}>30 seconds</option>\n                    <option value={60}>1 minute</option>\n                    <option value={300}>5 minutes</option>\n                  </select>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700\">\n          <button\n            onClick={handleReset}\n            className=\"flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n          >\n            <RotateCcw className=\"w-4 h-4\" />\n            <span>Reset</span>\n          </button>\n          \n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={handleSave}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <Save className=\"w-4 h-4\" />\n              <span>Save</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAae,SAAS,cAAc,EAAE,MAAM,EAAE,OAAO,EAAsB;IAC3E,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAC9D,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,gBAAgB;IAClB,GAAG;QAAC;KAAS;IAEb,MAAM,aAAa;QACjB,eAAe;QACf;IACF;IAEA,MAAM,cAAc;QAClB;QACA,gBAAgB;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;;;;;;;sCAIlE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,SAAS;oDACT,WAAU;8DAET,UAAU,SAAS,cAAc;;;;;;;;;;;;sDAItC,8OAAC;sDACC,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,aAAa,gBAAgB;wDACtC,UAAU,CAAC,IAAM,gBAAgB;gEAAE,GAAG,YAAY;gEAAE,kBAAkB,EAAE,MAAM,CAAC,OAAO;4DAAC;wDACvF,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;;;;;;sDAM/D,8OAAC;sDACC,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,aAAa,UAAU;wDAChC,UAAU,CAAC,IAAM,gBAAgB;gEAAE,GAAG,YAAY;gEAAE,YAAY,EAAE,MAAM,CAAC,OAAO;4DAAC;wDACjF,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;;;;;;sDAM/D,8OAAC;sDACC,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,aAAa,YAAY;wDAClC,UAAU,CAAC,IAAM,gBAAgB;gEAAE,GAAG,YAAY;gEAAE,cAAc,EAAE,MAAM,CAAC,OAAO;4DAAC;wDACnF,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASnE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,OAAO,aAAa,QAAQ;oDAC5B,UAAU,CAAC,IAAM,gBAAgB;4DAAE,GAAG,YAAY;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC7E,WAAU;8DAET,yHAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,CAAC,yBACzB,8OAAC;4DAA2B,OAAO,SAAS,IAAI;;gEAC7C,SAAS,IAAI;gEAAC;gEAAG,SAAS,MAAM;gEAAC;;2DADvB,SAAS,IAAI;;;;;;;;;;;;;;;;sDAOhC,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,OAAO,aAAa,YAAY;oDAChC,UAAU,CAAC,IAAM,gBAAgB;4DAAE,GAAG,YAAY;4DAAE,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAE;oDAC3F,WAAU;;sEAEV,8OAAC;4DAAO,OAAO;sEAAI;;;;;;sEACnB,8OAAC;4DAAO,OAAO;sEAAK;;;;;;sEACpB,8OAAC;4DAAO,OAAO;sEAAK;;;;;;sEACpB,8OAAC;4DAAO,OAAO;sEAAK;;;;;;;;;;;;;;;;;;sDAIxB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,OAAO,aAAa,iBAAiB;oDACrC,UAAU,CAAC,IAAM,gBAAgB;4DAAE,GAAG,YAAY;4DAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDAAQ;oDAC7F,WAAU;8DAET,yHAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;4DAAqB,OAAO,KAAK,EAAE;4DAAE,UAAU,KAAK,EAAE,KAAK,YAAY,KAAK,EAAE,KAAK;;gEACjF,KAAK,IAAI;gEAAC;gEAAE,KAAK,EAAE,KAAK,YAAY,KAAK,EAAE,KAAK,YAAY,kBAAkB;;2DADpE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU9B,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDACC,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,aAAa,WAAW;wDACjC,UAAU,CAAC,IAAM,gBAAgB;gEAAE,GAAG,YAAY;gEAAE,aAAa,EAAE,MAAM,CAAC,OAAO;4DAAC;wDAClF,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;;;;;;wCAM9D,aAAa,WAAW,kBACvB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,OAAO,aAAa,eAAe,GAAG;oDACtC,UAAU,CAAC,IAAM,gBAAgB;4DAAE,GAAG,YAAY;4DAAE,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;wDAAK;oDACrG,WAAU;;sEAEV,8OAAC;4DAAO,OAAO;sEAAI;;;;;;sEACnB,8OAAC;4DAAO,OAAO;sEAAI;;;;;;sEACnB,8OAAC;4DAAO,OAAO;sEAAI;;;;;;sEACnB,8OAAC;4DAAO,OAAO;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAShC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAK;;;;;;;;;;;;sCAGR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1731, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Refresh<PERSON><PERSON>, Moon, Sun, Menu, <PERSON>, Settings } from 'lucide-react';\nimport AdvancedSearch from './AdvancedSearch';\nimport SettingsPanel from './SettingsPanel';\nimport { useTheme } from '@/contexts/ThemeContext';\n\ninterface HeaderProps {\n  onRefresh?: () => void;\n  isRefreshing?: boolean;\n  onCryptoSearch?: (crypto: any) => void;\n}\n\nexport default function Header({\n  onRefresh,\n  isRefreshing = false,\n  onCryptoSearch\n}: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isSettingsOpen, setIsSettingsOpen] = useState(false);\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo and Title */}\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">CB</span>\n                </div>\n                <div className=\"hidden sm:block\">\n                  <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                    Crypto Bubble\n                  </h1>\n                  <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                    Market Visualization\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-4 flex-1 max-w-md mx-8\">\n            <AdvancedSearch\n              onCryptoSelect={onCryptoSearch}\n              placeholder=\"Search cryptocurrencies...\"\n              className=\"w-full\"\n            />\n          </div>\n\n          {/* Desktop Actions */}\n          <div className=\"hidden md:flex items-center space-x-3\">\n            <button\n              onClick={onRefresh}\n              disabled={isRefreshing}\n              className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n              title=\"Refresh data\"\n            >\n              <RefreshCw className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''}`} />\n            </button>\n            \n            <button\n              onClick={toggleTheme}\n              className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n              title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n            >\n              {isDarkMode ? (\n                <Sun className=\"w-5 h-5\" />\n              ) : (\n                <Moon className=\"w-5 h-5\" />\n              )}\n            </button>\n\n            <button\n              onClick={() => setIsSettingsOpen(true)}\n              className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n              title=\"Settings\"\n            >\n              <Settings className=\"w-5 h-5\" />\n            </button>\n\n            <div className=\"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span>Live</span>\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 dark:border-gray-700 py-4 space-y-4\">\n            {/* Mobile Search */}\n            <div className=\"px-2\">\n              <AdvancedSearch\n                onCryptoSelect={(crypto) => {\n                  onCryptoSearch?.(crypto);\n                  setIsMobileMenuOpen(false);\n                }}\n                placeholder=\"Search cryptocurrencies...\"\n                className=\"w-full\"\n              />\n            </div>\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-2\">\n              <div className=\"flex items-center space-x-3\">\n                <button\n                  onClick={() => {\n                    onRefresh?.();\n                    setIsMobileMenuOpen(false);\n                  }}\n                  disabled={isRefreshing}\n                  className=\"flex items-center space-x-2 px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50\"\n                >\n                  <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />\n                  <span className=\"text-sm\">Refresh</span>\n                </button>\n                \n                <button\n                  onClick={() => {\n                    toggleTheme();\n                    setIsMobileMenuOpen(false);\n                  }}\n                  className=\"flex items-center space-x-2 px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  {isDarkMode ? (\n                    <>\n                      <Sun className=\"w-4 h-4\" />\n                      <span className=\"text-sm\">Light</span>\n                    </>\n                  ) : (\n                    <>\n                      <Moon className=\"w-4 h-4\" />\n                      <span className=\"text-sm\">Dark</span>\n                    </>\n                  )}\n                </button>\n\n                <button\n                  onClick={() => {\n                    setIsSettingsOpen(true);\n                    setIsMobileMenuOpen(false);\n                  }}\n                  className=\"flex items-center space-x-2 px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  <Settings className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">Settings</span>\n                </button>\n              </div>\n\n              <div className=\"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span>Live</span>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Settings Panel */}\n      <SettingsPanel\n        isOpen={isSettingsOpen}\n        onClose={() => setIsSettingsOpen(false)}\n      />\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAce,SAAS,OAAO,EAC7B,SAAS,EACT,eAAe,KAAK,EACpB,cAAc,EACF;IACZ,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAE3C,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAkD;;;;;;kEAGhE,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAShE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;oCACb,gBAAgB;oCAChB,aAAY;oCACZ,WAAU;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,iBAAiB,IAAI;;;;;;;;;;;kDAGvE,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAO,aAAa,yBAAyB;kDAE5C,2BACC,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;iEAEf,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAIpB,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAGtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;8CAET,iCACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;oBAOvB,kCACC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;oCACb,gBAAgB,CAAC;wCACf,iBAAiB;wCACjB,oBAAoB;oCACtB;oCACA,aAAY;oCACZ,WAAU;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;oDACP;oDACA,oBAAoB;gDACtB;gDACA,UAAU;gDACV,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,iBAAiB,IAAI;;;;;;kEACrE,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAG5B,8OAAC;gDACC,SAAS;oDACP;oDACA,oBAAoB;gDACtB;gDACA,WAAU;0DAET,2BACC;;sEACE,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;iFAG5B;;sEACE,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;0DAKhC,8OAAC;gDACC,SAAS;oDACP,kBAAkB;oDAClB,oBAAoB;gDACtB;gDACA,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;kDAI9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC,mIAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,kBAAkB;;;;;;;;;;;;AAIzC", "debugId": null}}, {"offset": {"line": 2164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  text?: string;\n}\n\nexport default function LoadingSpinner({ \n  size = 'md', \n  className = '',\n  text = 'Loading...'\n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n  };\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg',\n  };\n\n  return (\n    <div className={`flex flex-col items-center justify-center space-y-2 ${className}`}>\n      <div className={`${sizeClasses[size]} animate-spin`}>\n        <svg\n          className=\"w-full h-full text-blue-600\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      </div>\n      {text && (\n        <p className={`text-gray-600 dark:text-gray-400 ${textSizeClasses[size]}`}>\n          {text}\n        </p>\n      )}\n    </div>\n  );\n}\n\nexport function BubbleChartSkeleton({ width = 800, height = 600 }: { width?: number; height?: number }) {\n  return (\n    <div \n      className=\"border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700 flex items-center justify-center\"\n      style={{ width, height }}\n    >\n      <LoadingSpinner size=\"lg\" text=\"Loading cryptocurrency data...\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAUe,SAAS,eAAe,EACrC,OAAO,IAAI,EACX,YAAY,EAAE,EACd,OAAO,YAAY,EACC;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,oDAAoD,EAAE,WAAW;;0BAChF,8OAAC;gBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC;0BACjD,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;;sCAER,8OAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;;;;;;YAIP,sBACC,8OAAC;gBAAE,WAAW,CAAC,iCAAiC,EAAE,eAAe,CAAC,KAAK,EAAE;0BACtE;;;;;;;;;;;;AAKX;AAEO,SAAS,oBAAoB,EAAE,QAAQ,GAAG,EAAE,SAAS,GAAG,EAAuC;IACpG,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAE;YAAO;QAAO;kBAEvB,cAAA,8OAAC;YAAe,MAAK;YAAK,MAAK;;;;;;;;;;;AAGrC", "debugId": null}}, {"offset": {"line": 2265, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/CryptoModal.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { X, TrendingUp, TrendingDown, ExternalLink } from 'lucide-react';\nimport { BubbleData } from '@/types/crypto';\nimport { useCoinDetails, useHistoricalData } from '@/hooks/useCrypto';\nimport { formatCurrency, formatPercentage } from '@/utils/dataTransform';\nimport LoadingSpinner from './LoadingSpinner';\n\ninterface CryptoModalProps {\n  crypto: BubbleData | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function CryptoModal({ crypto, isOpen, onClose }: CryptoModalProps) {\n  const { coinDetails, isLoading: detailsLoading } = useCoinDetails(crypto?.id || null);\n  const { historicalData, isLoading: historyLoading } = useHistoricalData(crypto?.id || null, 7);\n\n  if (!isOpen || !crypto) return null;\n\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <div \n      className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\"\n      onClick={handleBackdropClick}\n    >\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center space-x-3\">\n            <img \n              src={crypto.image} \n              alt={crypto.name}\n              className=\"w-10 h-10 rounded-full\"\n            />\n            <div>\n              <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                {crypto.name}\n              </h2>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {crypto.symbol} • Rank #{crypto.rank}\n              </p>\n            </div>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n          >\n            <X className=\"w-5 h-5 text-gray-500\" />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 space-y-6\">\n          {/* Price Information */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Current Price</p>\n              <p className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(crypto.price)}\n              </p>\n            </div>\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">24h Change</p>\n              <div className=\"flex items-center space-x-1\">\n                {crypto.change >= 0 ? (\n                  <TrendingUp className=\"w-4 h-4 text-green-500\" />\n                ) : (\n                  <TrendingDown className=\"w-4 h-4 text-red-500\" />\n                )}\n                <p className={`text-lg font-bold ${\n                  crypto.change >= 0 ? 'text-green-600' : 'text-red-600'\n                }`}>\n                  {formatPercentage(crypto.change)}\n                </p>\n              </div>\n            </div>\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Market Cap</p>\n              <p className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(crypto.value)}\n              </p>\n            </div>\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Volume (24h)</p>\n              <p className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(crypto.volume)}\n              </p>\n            </div>\n          </div>\n\n          {/* Additional Details */}\n          {detailsLoading ? (\n            <div className=\"flex justify-center py-8\">\n              <LoadingSpinner text=\"Loading details...\" />\n            </div>\n          ) : coinDetails ? (\n            <div className=\"space-y-4\">\n              {/* Description */}\n              {coinDetails.description?.en && (\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                    About {crypto.name}\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-400 text-sm leading-relaxed\">\n                    {coinDetails.description.en.split('.')[0]}.\n                  </p>\n                </div>\n              )}\n\n              {/* Market Data */}\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n                  Market Data\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {coinDetails.market_data?.ath?.usd && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">All-Time High:</span>\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {formatCurrency(coinDetails.market_data.ath.usd)}\n                      </span>\n                    </div>\n                  )}\n                  {coinDetails.market_data?.atl?.usd && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">All-Time Low:</span>\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {formatCurrency(coinDetails.market_data.atl.usd)}\n                      </span>\n                    </div>\n                  )}\n                  {coinDetails.market_data?.circulating_supply && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Circulating Supply:</span>\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {coinDetails.market_data.circulating_supply.toLocaleString()}\n                      </span>\n                    </div>\n                  )}\n                  {coinDetails.market_data?.max_supply && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Supply:</span>\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {coinDetails.market_data.max_supply.toLocaleString()}\n                      </span>\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Links */}\n              {coinDetails.links && (\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n                    Links\n                  </h3>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {coinDetails.links.homepage?.[0] && (\n                      <a\n                        href={coinDetails.links.homepage[0]}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"inline-flex items-center space-x-1 px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors\"\n                      >\n                        <span>Website</span>\n                        <ExternalLink className=\"w-3 h-3\" />\n                      </a>\n                    )}\n                    {coinDetails.links.twitter_screen_name && (\n                      <a\n                        href={`https://twitter.com/${coinDetails.links.twitter_screen_name}`}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"inline-flex items-center space-x-1 px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors\"\n                      >\n                        <span>Twitter</span>\n                        <ExternalLink className=\"w-3 h-3\" />\n                      </a>\n                    )}\n                    {coinDetails.links.repos_url?.github?.[0] && (\n                      <a\n                        href={coinDetails.links.repos_url.github[0]}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"inline-flex items-center space-x-1 px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n                      >\n                        <span>GitHub</span>\n                        <ExternalLink className=\"w-3 h-3\" />\n                      </a>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          ) : null}\n\n          {/* Price History Chart Placeholder */}\n          {historyLoading ? (\n            <div className=\"flex justify-center py-8\">\n              <LoadingSpinner text=\"Loading price history...\" />\n            </div>\n          ) : historicalData ? (\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n                7-Day Price History\n              </h3>\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Price chart will be implemented in the next phase\n                </p>\n              </div>\n            </div>\n          ) : null}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAPA;;;;;;AAee,SAAS,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAoB;IAC/E,MAAM,EAAE,WAAW,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;IAChF,MAAM,EAAE,cAAc,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,MAAM,MAAM;IAE5F,IAAI,CAAC,UAAU,CAAC,QAAQ,OAAO;IAE/B,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC;QACF;IACF;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK,OAAO,KAAK;oCACjB,KAAK,OAAO,IAAI;oCAChB,WAAU;;;;;;8CAEZ,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,OAAO,IAAI;;;;;;sDAEd,8OAAC;4CAAE,WAAU;;gDACV,OAAO,MAAM;gDAAC;gDAAU,OAAO,IAAI;;;;;;;;;;;;;;;;;;;sCAI1C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,8OAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK;;;;;;;;;;;;8CAGhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,8OAAC;4CAAI,WAAU;;gDACZ,OAAO,MAAM,IAAI,kBAChB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;yEAEtB,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DAE1B,8OAAC;oDAAE,WAAW,CAAC,kBAAkB,EAC/B,OAAO,MAAM,IAAI,IAAI,mBAAmB,gBACxC;8DACC,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,MAAM;;;;;;;;;;;;;;;;;;8CAIrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,8OAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK;;;;;;;;;;;;8CAGhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,8OAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM;;;;;;;;;;;;;;;;;;wBAMlC,+BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;gCAAC,MAAK;;;;;;;;;;mCAErB,4BACF,8OAAC;4BAAI,WAAU;;gCAEZ,YAAY,WAAW,EAAE,oBACxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAA2D;gDAChE,OAAO,IAAI;;;;;;;sDAEpB,8OAAC;4CAAE,WAAU;;gDACV,YAAY,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gDAAC;;;;;;;;;;;;;8CAMhD,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,WAAW,EAAE,KAAK,qBAC7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;sEAC3D,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,WAAW,CAAC,GAAG,CAAC,GAAG;;;;;;;;;;;;gDAIpD,YAAY,WAAW,EAAE,KAAK,qBAC7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;sEAC3D,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,WAAW,CAAC,GAAG,CAAC,GAAG;;;;;;;;;;;;gDAIpD,YAAY,WAAW,EAAE,oCACxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;sEAC3D,8OAAC;4DAAK,WAAU;sEACb,YAAY,WAAW,CAAC,kBAAkB,CAAC,cAAc;;;;;;;;;;;;gDAI/D,YAAY,WAAW,EAAE,4BACxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;sEAC3D,8OAAC;4DAAK,WAAU;sEACb,YAAY,WAAW,CAAC,UAAU,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;gCAQ3D,YAAY,KAAK,kBAChB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,KAAK,CAAC,QAAQ,EAAE,CAAC,EAAE,kBAC9B,8OAAC;oDACC,MAAM,YAAY,KAAK,CAAC,QAAQ,CAAC,EAAE;oDACnC,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC;sEAAK;;;;;;sEACN,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;gDAG3B,YAAY,KAAK,CAAC,mBAAmB,kBACpC,8OAAC;oDACC,MAAM,CAAC,oBAAoB,EAAE,YAAY,KAAK,CAAC,mBAAmB,EAAE;oDACpE,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC;sEAAK;;;;;;sEACN,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;gDAG3B,YAAY,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,kBACvC,8OAAC;oDACC,MAAM,YAAY,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;oDAC3C,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC;sEAAK;;;;;;sEACN,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAOlC;wBAGH,+BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;gCAAC,MAAK;;;;;;;;;;mCAErB,+BACF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;mCAK1D;;;;;;;;;;;;;;;;;;AAKd", "debugId": null}}, {"offset": {"line": 2847, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/FilterPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Filter, ChevronDown, ChevronUp } from 'lucide-react';\nimport { FilterOptions } from '@/types/crypto';\nimport { MARKET_CAP_RANGES, VOLUME_RANGES, PRICE_CHANGE_RANGES } from '@/constants';\n\ninterface FilterPanelProps {\n  filters: FilterOptions;\n  onFiltersChange: (filters: FilterOptions) => void;\n  className?: string;\n}\n\nexport default function FilterPanel({ \n  filters, \n  onFiltersChange, \n  className = \"\" \n}: FilterPanelProps) {\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  const handleMarketCapChange = (range: typeof MARKET_CAP_RANGES[0]) => {\n    onFiltersChange({\n      ...filters,\n      minMarketCap: range.min === 0 ? undefined : range.min,\n      maxMarketCap: range.max === Infinity ? undefined : range.max,\n    });\n  };\n\n  const handleVolumeChange = (range: typeof VOLUME_RANGES[0]) => {\n    onFiltersChange({\n      ...filters,\n      minVolume: range.min === 0 ? undefined : range.min,\n      maxVolume: range.max === Infinity ? undefined : range.max,\n    });\n  };\n\n  const handlePriceChangeChange = (range: typeof PRICE_CHANGE_RANGES[0]) => {\n    onFiltersChange({\n      ...filters,\n      minPriceChange: range.min === -Infinity ? undefined : range.min,\n      maxPriceChange: range.max === Infinity ? undefined : range.max,\n    });\n  };\n\n  const clearFilters = () => {\n    onFiltersChange({});\n  };\n\n  const hasActiveFilters = Object.keys(filters).some(key => \n    filters[key as keyof FilterOptions] !== undefined\n  );\n\n  return (\n    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>\n      {/* Header */}\n      <button\n        onClick={() => setIsExpanded(!isExpanded)}\n        className=\"w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n      >\n        <div className=\"flex items-center space-x-2\">\n          <Filter className=\"w-5 h-5 text-gray-500\" />\n          <span className=\"font-medium text-gray-900 dark:text-white\">\n            Filters\n          </span>\n          {hasActiveFilters && (\n            <span className=\"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full\">\n              Active\n            </span>\n          )}\n        </div>\n        {isExpanded ? (\n          <ChevronUp className=\"w-5 h-5 text-gray-500\" />\n        ) : (\n          <ChevronDown className=\"w-5 h-5 text-gray-500\" />\n        )}\n      </button>\n\n      {/* Filter Content */}\n      {isExpanded && (\n        <div className=\"px-4 pb-4 space-y-4 border-t border-gray-200 dark:border-gray-700\">\n          {/* Market Cap Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Market Cap\n            </label>\n            <div className=\"space-y-1\">\n              {MARKET_CAP_RANGES.map((range) => (\n                <label key={range.label} className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"marketCap\"\n                    checked={\n                      (filters.minMarketCap || 0) === range.min &&\n                      (filters.maxMarketCap || Infinity) === range.max\n                    }\n                    onChange={() => handleMarketCapChange(range)}\n                    className=\"mr-2 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {range.label}\n                  </span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* Volume Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              24h Volume\n            </label>\n            <div className=\"space-y-1\">\n              {VOLUME_RANGES.map((range) => (\n                <label key={range.label} className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"volume\"\n                    checked={\n                      (filters.minVolume || 0) === range.min &&\n                      (filters.maxVolume || Infinity) === range.max\n                    }\n                    onChange={() => handleVolumeChange(range)}\n                    className=\"mr-2 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {range.label}\n                  </span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* Price Change Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              24h Price Change\n            </label>\n            <div className=\"space-y-1\">\n              {PRICE_CHANGE_RANGES.map((range) => (\n                <label key={range.label} className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"priceChange\"\n                    checked={\n                      (filters.minPriceChange || -Infinity) === range.min &&\n                      (filters.maxPriceChange || Infinity) === range.max\n                    }\n                    onChange={() => handlePriceChangeChange(range)}\n                    className=\"mr-2 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {range.label}\n                  </span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* Clear Filters */}\n          {hasActiveFilters && (\n            <button\n              onClick={clearFilters}\n              className=\"w-full px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n            >\n              Clear All Filters\n            </button>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AALA;;;;;AAae,SAAS,YAAY,EAClC,OAAO,EACP,eAAe,EACf,YAAY,EAAE,EACG;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,wBAAwB,CAAC;QAC7B,gBAAgB;YACd,GAAG,OAAO;YACV,cAAc,MAAM,GAAG,KAAK,IAAI,YAAY,MAAM,GAAG;YACrD,cAAc,MAAM,GAAG,KAAK,WAAW,YAAY,MAAM,GAAG;QAC9D;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;YACd,GAAG,OAAO;YACV,WAAW,MAAM,GAAG,KAAK,IAAI,YAAY,MAAM,GAAG;YAClD,WAAW,MAAM,GAAG,KAAK,WAAW,YAAY,MAAM,GAAG;QAC3D;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,gBAAgB;YACd,GAAG,OAAO;YACV,gBAAgB,MAAM,GAAG,KAAK,CAAC,WAAW,YAAY,MAAM,GAAG;YAC/D,gBAAgB,MAAM,GAAG,KAAK,WAAW,YAAY,MAAM,GAAG;QAChE;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB,CAAC;IACnB;IAEA,MAAM,mBAAmB,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,CAAA,MACjD,OAAO,CAAC,IAA2B,KAAK;IAG1C,qBACE,8OAAC;QAAI,WAAW,CAAC,iFAAiF,EAAE,WAAW;;0BAE7G,8OAAC;gBACC,SAAS,IAAM,cAAc,CAAC;gBAC9B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAK,WAAU;0CAA4C;;;;;;4BAG3D,kCACC,8OAAC;gCAAK,WAAU;0CAA+F;;;;;;;;;;;;oBAKlH,2BACC,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;6CAErB,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;YAK1B,4BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCAAI,WAAU;0CACZ,yHAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAC,sBACtB,8OAAC;wCAAwB,WAAU;;0DACjC,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,SACE,CAAC,QAAQ,YAAY,IAAI,CAAC,MAAM,MAAM,GAAG,IACzC,CAAC,QAAQ,YAAY,IAAI,QAAQ,MAAM,MAAM,GAAG;gDAElD,UAAU,IAAM,sBAAsB;gDACtC,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,MAAM,KAAK;;;;;;;uCAZJ,MAAM,KAAK;;;;;;;;;;;;;;;;kCAoB7B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCAAI,WAAU;0CACZ,yHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC;wCAAwB,WAAU;;0DACjC,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,SACE,CAAC,QAAQ,SAAS,IAAI,CAAC,MAAM,MAAM,GAAG,IACtC,CAAC,QAAQ,SAAS,IAAI,QAAQ,MAAM,MAAM,GAAG;gDAE/C,UAAU,IAAM,mBAAmB;gDACnC,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,MAAM,KAAK;;;;;;;uCAZJ,MAAM,KAAK;;;;;;;;;;;;;;;;kCAoB7B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCAAI,WAAU;0CACZ,yHAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAC,sBACxB,8OAAC;wCAAwB,WAAU;;0DACjC,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,SACE,CAAC,QAAQ,cAAc,IAAI,CAAC,QAAQ,MAAM,MAAM,GAAG,IACnD,CAAC,QAAQ,cAAc,IAAI,QAAQ,MAAM,MAAM,GAAG;gDAEpD,UAAU,IAAM,wBAAwB;gDACxC,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,MAAM,KAAK;;;;;;;uCAZJ,MAAM,KAAK;;;;;;;;;;;;;;;;oBAoB5B,kCACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 3128, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/VisualizationModeSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>3, <PERSON>rid3<PERSON>3, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';\nimport { VisualizationMode } from '@/types/crypto';\nimport { VISUALIZATION_MODES } from '@/constants';\n\ninterface VisualizationModeSelectorProps {\n  currentMode: VisualizationMode['id'];\n  onModeChange: (mode: VisualizationMode['id']) => void;\n  className?: string;\n}\n\nconst modeIcons = {\n  bubble: BarChart3,\n  treemap: Grid3X3,\n  scatter: Scatter<PERSON><PERSON>,\n  heatmap: Flame,\n};\n\nexport default function VisualizationModeSelector({\n  currentMode,\n  onModeChange,\n  className = '',\n}: VisualizationModeSelectorProps) {\n  return (\n    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 ${className}`}>\n      <h3 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">\n        Visualization Mode\n      </h3>\n      \n      <div className=\"grid grid-cols-2 gap-2\">\n        {VISUALIZATION_MODES.map((mode) => {\n          const Icon = modeIcons[mode.id];\n          const isActive = currentMode === mode.id;\n          const isDisabled = mode.id !== 'bubble'; // Only bubble chart is implemented for now\n          \n          return (\n            <button\n              key={mode.id}\n              onClick={() => !isDisabled && onModeChange(mode.id)}\n              disabled={isDisabled}\n              className={`\n                relative p-3 rounded-lg border-2 transition-all duration-200 text-left\n                ${isActive \n                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' \n                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\n                }\n                ${isDisabled \n                  ? 'opacity-50 cursor-not-allowed' \n                  : 'cursor-pointer hover:shadow-sm'\n                }\n              `}\n              title={isDisabled ? 'Coming soon' : mode.description}\n            >\n              <div className=\"flex items-center space-x-2 mb-1\">\n                <Icon className={`w-4 h-4 ${\n                  isActive \n                    ? 'text-blue-600 dark:text-blue-400' \n                    : 'text-gray-500 dark:text-gray-400'\n                }`} />\n                <span className={`text-sm font-medium ${\n                  isActive \n                    ? 'text-blue-900 dark:text-blue-100' \n                    : 'text-gray-900 dark:text-white'\n                }`}>\n                  {mode.name}\n                </span>\n              </div>\n              \n              <p className={`text-xs ${\n                isActive \n                  ? 'text-blue-700 dark:text-blue-300' \n                  : 'text-gray-600 dark:text-gray-400'\n              }`}>\n                {mode.description}\n              </p>\n              \n              {isDisabled && (\n                <div className=\"absolute top-1 right-1\">\n                  <span className=\"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\">\n                    Soon\n                  </span>\n                </div>\n              )}\n              \n              {isActive && (\n                <div className=\"absolute top-1 right-1\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                </div>\n              )}\n            </button>\n          );\n        })}\n      </div>\n      \n      <div className=\"mt-3 text-xs text-gray-500 dark:text-gray-400\">\n        More visualization modes coming soon!\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAEA;AALA;;;;AAaA,MAAM,YAAY;IAChB,QAAQ,kNAAA,CAAA,YAAS;IACjB,SAAS,4MAAA,CAAA,UAAO;IAChB,SAAS,sNAAA,CAAA,eAAY;IACrB,SAAS,oMAAA,CAAA,QAAK;AAChB;AAEe,SAAS,0BAA0B,EAChD,WAAW,EACX,YAAY,EACZ,YAAY,EAAE,EACiB;IAC/B,qBACE,8OAAC;QAAI,WAAW,CAAC,qFAAqF,EAAE,WAAW;;0BACjH,8OAAC;gBAAG,WAAU;0BAAyD;;;;;;0BAIvE,8OAAC;gBAAI,WAAU;0BACZ,yHAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAC;oBACxB,MAAM,OAAO,SAAS,CAAC,KAAK,EAAE,CAAC;oBAC/B,MAAM,WAAW,gBAAgB,KAAK,EAAE;oBACxC,MAAM,aAAa,KAAK,EAAE,KAAK,UAAU,2CAA2C;oBAEpF,qBACE,8OAAC;wBAEC,SAAS,IAAM,CAAC,cAAc,aAAa,KAAK,EAAE;wBAClD,UAAU;wBACV,WAAW,CAAC;;gBAEV,EAAE,WACE,mDACA,wFACH;gBACD,EAAE,aACE,kCACA,iCACH;cACH,CAAC;wBACD,OAAO,aAAa,gBAAgB,KAAK,WAAW;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAC,QAAQ,EACxB,WACI,qCACA,oCACJ;;;;;;kDACF,8OAAC;wCAAK,WAAW,CAAC,oBAAoB,EACpC,WACI,qCACA,iCACJ;kDACC,KAAK,IAAI;;;;;;;;;;;;0CAId,8OAAC;gCAAE,WAAW,CAAC,QAAQ,EACrB,WACI,qCACA,oCACJ;0CACC,KAAK,WAAW;;;;;;4BAGlB,4BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgJ;;;;;;;;;;;4BAMnK,0BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;;uBAjDd,KAAK,EAAE;;;;;gBAsDlB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BAAgD;;;;;;;;;;;;AAKrE", "debugId": null}}, {"offset": {"line": 3267, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/BubbleChart.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as d3 from 'd3';\nimport { BubbleData } from '@/types/crypto';\nimport { calculateBubbleRadius, getPriceChangeColor, formatCurrency, formatPercentage } from '@/utils/dataTransform';\nimport { BUBBLE_CONFIG, COLORS } from '@/constants';\n\ninterface BubbleChartProps {\n  data: BubbleData[];\n  width?: number;\n  height?: number;\n  onBubbleClick?: (data: BubbleData) => void;\n  onBubbleHover?: (data: BubbleData | null) => void;\n  className?: string;\n}\n\ninterface TooltipData {\n  data: BubbleData;\n  x: number;\n  y: number;\n}\n\nexport default function BubbleChart({\n  data,\n  width = 800,\n  height = 600,\n  onBubbleClick,\n  onBubbleHover,\n  className = '',\n}: BubbleChartProps) {\n  const svgRef = useRef<SVGSVGElement>(null);\n  const [tooltip, setTooltip] = useState<TooltipData | null>(null);\n\n  useEffect(() => {\n    if (!data.length || !svgRef.current) return;\n\n    const svg = d3.select(svgRef.current);\n    svg.selectAll('*').remove();\n\n    // Calculate bubble radii\n    const marketCaps = data.map(d => d.value);\n    const bubbleData = data.map(d => ({\n      ...d,\n      radius: calculateBubbleRadius(d.value, BUBBLE_CONFIG.MIN_RADIUS, BUBBLE_CONFIG.MAX_RADIUS, marketCaps),\n    }));\n\n    // Create simulation\n    const simulation = d3.forceSimulation(bubbleData as any)\n      .force('charge', d3.forceManyBody().strength(BUBBLE_CONFIG.CHARGE_STRENGTH))\n      .force('center', d3.forceCenter(width / 2, height / 2))\n      .force('collision', d3.forceCollide().radius((d: any) => d.radius + BUBBLE_CONFIG.PADDING).strength(BUBBLE_CONFIG.COLLISION_STRENGTH));\n\n    // Create bubbles\n    const bubbles = svg\n      .selectAll('circle')\n      .data(bubbleData)\n      .enter()\n      .append('circle')\n      .attr('r', 0)\n      .attr('fill', d => getPriceChangeColor(d.change))\n      .attr('stroke', '#fff')\n      .attr('stroke-width', 1)\n      .style('cursor', 'pointer')\n      .style('opacity', 0.8);\n\n    // Create labels\n    const labels = svg\n      .selectAll('text')\n      .data(bubbleData.filter(d => d.radius > 20)) // Only show labels for larger bubbles\n      .enter()\n      .append('text')\n      .attr('text-anchor', 'middle')\n      .attr('dy', '0.3em')\n      .style('font-size', d => `${Math.min(d.radius / 3, 14)}px`)\n      .style('font-weight', 'bold')\n      .style('fill', '#fff')\n      .style('pointer-events', 'none')\n      .text(d => d.symbol);\n\n    // Animation\n    bubbles\n      .transition()\n      .duration(BUBBLE_CONFIG.ANIMATION_DURATION)\n      .attr('r', d => d.radius)\n      .style('opacity', 0.8);\n\n    // Event handlers\n    bubbles\n      .on('mouseover', function(event, d) {\n        d3.select(this)\n          .transition()\n          .duration(200)\n          .style('opacity', 1)\n          .attr('stroke-width', 2);\n\n        const [x, y] = d3.pointer(event, svgRef.current);\n        setTooltip({ data: d, x, y });\n        onBubbleHover?.(d);\n      })\n      .on('mouseout', function(event, d) {\n        d3.select(this)\n          .transition()\n          .duration(200)\n          .style('opacity', 0.8)\n          .attr('stroke-width', 1);\n\n        setTooltip(null);\n        onBubbleHover?.(null);\n      })\n      .on('click', function(event, d) {\n        onBubbleClick?.(d);\n      });\n\n    // Update positions on simulation tick\n    simulation.on('tick', () => {\n      bubbles\n        .attr('cx', (d: any) => d.x)\n        .attr('cy', (d: any) => d.y);\n\n      labels\n        .attr('x', (d: any) => d.x)\n        .attr('y', (d: any) => d.y);\n    });\n\n    // Cleanup\n    return () => {\n      simulation.stop();\n    };\n  }, [data, width, height, onBubbleClick, onBubbleHover]);\n\n  return (\n    <div className={`relative ${className}`}>\n      <svg\n        ref={svgRef}\n        width={width}\n        height={height}\n        className=\"border border-gray-200 rounded-lg bg-white dark:bg-gray-900 dark:border-gray-700\"\n      />\n      \n      {tooltip && (\n        <div\n          className=\"absolute z-10 bg-black bg-opacity-90 text-white p-3 rounded-lg shadow-lg pointer-events-none\"\n          style={{\n            left: tooltip.x + 10,\n            top: tooltip.y - 10,\n            transform: tooltip.x > width - 200 ? 'translateX(-100%)' : 'none',\n          }}\n        >\n          <div className=\"font-bold text-lg\">{tooltip.data.name}</div>\n          <div className=\"text-sm text-gray-300\">{tooltip.data.symbol}</div>\n          <div className=\"mt-2 space-y-1\">\n            <div>Price: {formatCurrency(tooltip.data.price)}</div>\n            <div>Market Cap: {formatCurrency(tooltip.data.value)}</div>\n            <div>Volume: {formatCurrency(tooltip.data.volume)}</div>\n            <div className={`${tooltip.data.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>\n              24h Change: {formatPercentage(tooltip.data.change)}\n            </div>\n            <div className=\"text-xs text-gray-400\">Rank #{tooltip.data.rank}</div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AANA;;;;;;AAuBe,SAAS,YAAY,EAClC,IAAI,EACJ,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,aAAa,EACb,aAAa,EACb,YAAY,EAAE,EACG;IACjB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,OAAO,OAAO,EAAE;QAErC,MAAM,MAAM,qLAAA,CAAA,SAAS,CAAC,OAAO,OAAO;QACpC,IAAI,SAAS,CAAC,KAAK,MAAM;QAEzB,yBAAyB;QACzB,MAAM,aAAa,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACxC,MAAM,aAAa,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC;gBAChC,GAAG,CAAC;gBACJ,QAAQ,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,EAAE,EAAE,KAAK,EAAE,yHAAA,CAAA,gBAAa,CAAC,UAAU,EAAE,yHAAA,CAAA,gBAAa,CAAC,UAAU,EAAE;YAC7F,CAAC;QAED,oBAAoB;QACpB,MAAM,aAAa,8LAAA,CAAA,kBAAkB,CAAC,YACnC,KAAK,CAAC,UAAU,0LAAA,CAAA,gBAAgB,GAAG,QAAQ,CAAC,yHAAA,CAAA,gBAAa,CAAC,eAAe,GACzE,KAAK,CAAC,UAAU,sLAAA,CAAA,cAAc,CAAC,QAAQ,GAAG,SAAS,IACnD,KAAK,CAAC,aAAa,wLAAA,CAAA,eAAe,GAAG,MAAM,CAAC,CAAC,IAAW,EAAE,MAAM,GAAG,yHAAA,CAAA,gBAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,yHAAA,CAAA,gBAAa,CAAC,kBAAkB;QAEtI,iBAAiB;QACjB,MAAM,UAAU,IACb,SAAS,CAAC,UACV,IAAI,CAAC,YACL,KAAK,GACL,MAAM,CAAC,UACP,IAAI,CAAC,KAAK,GACV,IAAI,CAAC,QAAQ,CAAA,IAAK,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,MAAM,GAC9C,IAAI,CAAC,UAAU,QACf,IAAI,CAAC,gBAAgB,GACrB,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,WAAW;QAEpB,gBAAgB;QAChB,MAAM,SAAS,IACZ,SAAS,CAAC,QACV,IAAI,CAAC,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,KAAK,sCAAsC;SAClF,KAAK,GACL,MAAM,CAAC,QACP,IAAI,CAAC,eAAe,UACpB,IAAI,CAAC,MAAM,SACX,KAAK,CAAC,aAAa,CAAA,IAAK,GAAG,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC,EACzD,KAAK,CAAC,eAAe,QACrB,KAAK,CAAC,QAAQ,QACd,KAAK,CAAC,kBAAkB,QACxB,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM;QAErB,YAAY;QACZ,QACG,UAAU,GACV,QAAQ,CAAC,yHAAA,CAAA,gBAAa,CAAC,kBAAkB,EACzC,IAAI,CAAC,KAAK,CAAA,IAAK,EAAE,MAAM,EACvB,KAAK,CAAC,WAAW;QAEpB,iBAAiB;QACjB,QACG,EAAE,CAAC,aAAa,SAAS,KAAK,EAAE,CAAC;YAChC,qLAAA,CAAA,SAAS,CAAC,IAAI,EACX,UAAU,GACV,QAAQ,CAAC,KACT,KAAK,CAAC,WAAW,GACjB,IAAI,CAAC,gBAAgB;YAExB,MAAM,CAAC,GAAG,EAAE,GAAG,uLAAA,CAAA,UAAU,CAAC,OAAO,OAAO,OAAO;YAC/C,WAAW;gBAAE,MAAM;gBAAG;gBAAG;YAAE;YAC3B,gBAAgB;QAClB,GACC,EAAE,CAAC,YAAY,SAAS,KAAK,EAAE,CAAC;YAC/B,qLAAA,CAAA,SAAS,CAAC,IAAI,EACX,UAAU,GACV,QAAQ,CAAC,KACT,KAAK,CAAC,WAAW,KACjB,IAAI,CAAC,gBAAgB;YAExB,WAAW;YACX,gBAAgB;QAClB,GACC,EAAE,CAAC,SAAS,SAAS,KAAK,EAAE,CAAC;YAC5B,gBAAgB;QAClB;QAEF,sCAAsC;QACtC,WAAW,EAAE,CAAC,QAAQ;YACpB,QACG,IAAI,CAAC,MAAM,CAAC,IAAW,EAAE,CAAC,EAC1B,IAAI,CAAC,MAAM,CAAC,IAAW,EAAE,CAAC;YAE7B,OACG,IAAI,CAAC,KAAK,CAAC,IAAW,EAAE,CAAC,EACzB,IAAI,CAAC,KAAK,CAAC,IAAW,EAAE,CAAC;QAC9B;QAEA,UAAU;QACV,OAAO;YACL,WAAW,IAAI;QACjB;IACF,GAAG;QAAC;QAAM;QAAO;QAAQ;QAAe;KAAc;IAEtD,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,8OAAC;gBACC,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAU;;;;;;YAGX,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,MAAM,QAAQ,CAAC,GAAG;oBAClB,KAAK,QAAQ,CAAC,GAAG;oBACjB,WAAW,QAAQ,CAAC,GAAG,QAAQ,MAAM,sBAAsB;gBAC7D;;kCAEA,8OAAC;wBAAI,WAAU;kCAAqB,QAAQ,IAAI,CAAC,IAAI;;;;;;kCACrD,8OAAC;wBAAI,WAAU;kCAAyB,QAAQ,IAAI,CAAC,MAAM;;;;;;kCAC3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAI;oCAAQ,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,KAAK;;;;;;;0CAC9C,8OAAC;;oCAAI;oCAAa,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,KAAK;;;;;;;0CACnD,8OAAC;;oCAAI;oCAAS,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,MAAM;;;;;;;0CAChD,8OAAC;gCAAI,WAAW,GAAG,QAAQ,IAAI,CAAC,MAAM,IAAI,IAAI,mBAAmB,gBAAgB;;oCAAE;oCACpE,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,IAAI,CAAC,MAAM;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;oCAAwB;oCAAO,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;AAM3E", "debugId": null}}, {"offset": {"line": 3457, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/TreemapChart.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as d3 from 'd3';\nimport { BubbleData } from '@/types/crypto';\nimport { getPriceChangeColor, formatCurrency, formatPercentage } from '@/utils/dataTransform';\n\ninterface TreemapChartProps {\n  data: BubbleData[];\n  width?: number;\n  height?: number;\n  onCellClick?: (data: BubbleData) => void;\n  onCellHover?: (data: BubbleData | null) => void;\n  className?: string;\n}\n\ninterface TooltipData {\n  data: BubbleData;\n  x: number;\n  y: number;\n}\n\nexport default function TreemapChart({\n  data,\n  width = 800,\n  height = 600,\n  onCellClick,\n  onCellHover,\n  className = '',\n}: TreemapChartProps) {\n  const svgRef = useRef<SVGSVGElement>(null);\n  const [tooltip, setTooltip] = useState<TooltipData | null>(null);\n\n  useEffect(() => {\n    if (!data.length || !svgRef.current) return;\n\n    const svg = d3.select(svgRef.current);\n    svg.selectAll('*').remove();\n\n    // Prepare data for treemap\n    const root = d3.hierarchy({ children: data })\n      .sum(d => d.value || 0)\n      .sort((a, b) => (b.value || 0) - (a.value || 0));\n\n    // Create treemap layout\n    const treemap = d3.treemap()\n      .size([width, height])\n      .padding(2)\n      .round(true);\n\n    treemap(root);\n\n    // Create cells\n    const cells = svg\n      .selectAll('g')\n      .data(root.leaves())\n      .enter()\n      .append('g')\n      .attr('transform', d => `translate(${d.x0},${d.y0})`);\n\n    // Add rectangles\n    cells\n      .append('rect')\n      .attr('width', d => d.x1 - d.x0)\n      .attr('height', d => d.y1 - d.y0)\n      .attr('fill', d => getPriceChangeColor(d.data.change))\n      .attr('stroke', '#fff')\n      .attr('stroke-width', 1)\n      .style('cursor', 'pointer')\n      .style('opacity', 0.8)\n      .on('mouseover', function(event, d) {\n        d3.select(this)\n          .transition()\n          .duration(200)\n          .style('opacity', 1)\n          .attr('stroke-width', 2);\n\n        const [x, y] = d3.pointer(event, svgRef.current);\n        setTooltip({ data: d.data, x, y });\n        onCellHover?.(d.data);\n      })\n      .on('mouseout', function(event, d) {\n        d3.select(this)\n          .transition()\n          .duration(200)\n          .style('opacity', 0.8)\n          .attr('stroke-width', 1);\n\n        setTooltip(null);\n        onCellHover?.(null);\n      })\n      .on('click', function(event, d) {\n        onCellClick?.(d.data);\n      });\n\n    // Add text labels\n    cells\n      .append('text')\n      .attr('x', 4)\n      .attr('y', 16)\n      .style('font-size', d => {\n        const cellWidth = d.x1 - d.x0;\n        const cellHeight = d.y1 - d.y0;\n        const minDimension = Math.min(cellWidth, cellHeight);\n        return `${Math.max(10, Math.min(14, minDimension / 8))}px`;\n      })\n      .style('font-weight', 'bold')\n      .style('fill', '#fff')\n      .style('pointer-events', 'none')\n      .text(d => {\n        const cellWidth = d.x1 - d.x0;\n        return cellWidth > 60 ? d.data.symbol : '';\n      });\n\n    // Add price labels for larger cells\n    cells\n      .filter(d => (d.x1 - d.x0) > 80 && (d.y1 - d.y0) > 40)\n      .append('text')\n      .attr('x', 4)\n      .attr('y', 32)\n      .style('font-size', '11px')\n      .style('fill', '#fff')\n      .style('opacity', 0.9)\n      .style('pointer-events', 'none')\n      .text(d => formatCurrency(d.data.price));\n\n    // Add change percentage for larger cells\n    cells\n      .filter(d => (d.x1 - d.x0) > 100 && (d.y1 - d.y0) > 60)\n      .append('text')\n      .attr('x', 4)\n      .attr('y', 46)\n      .style('font-size', '10px')\n      .style('fill', '#fff')\n      .style('opacity', 0.8)\n      .style('pointer-events', 'none')\n      .text(d => `${d.data.change >= 0 ? '+' : ''}${d.data.change.toFixed(2)}%`);\n\n  }, [data, width, height, onCellClick, onCellHover]);\n\n  return (\n    <div className={`relative ${className}`}>\n      <svg\n        ref={svgRef}\n        width={width}\n        height={height}\n        className=\"border border-gray-200 rounded-lg bg-white dark:bg-gray-900 dark:border-gray-700\"\n      />\n      \n      {tooltip && (\n        <div\n          className=\"absolute z-10 bg-black bg-opacity-90 text-white p-3 rounded-lg shadow-lg pointer-events-none\"\n          style={{\n            left: tooltip.x + 10,\n            top: tooltip.y - 10,\n            transform: tooltip.x > width - 200 ? 'translateX(-100%)' : 'none',\n          }}\n        >\n          <div className=\"font-bold text-lg\">{tooltip.data.name}</div>\n          <div className=\"text-sm text-gray-300\">{tooltip.data.symbol}</div>\n          <div className=\"mt-2 space-y-1\">\n            <div>Price: {formatCurrency(tooltip.data.price)}</div>\n            <div>Market Cap: {formatCurrency(tooltip.data.value)}</div>\n            <div>Volume: {formatCurrency(tooltip.data.volume)}</div>\n            <div className={`${tooltip.data.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>\n              24h Change: {formatPercentage(tooltip.data.change)}\n            </div>\n            <div className=\"text-xs text-gray-400\">Rank #{tooltip.data.rank}</div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AALA;;;;;AAsBe,SAAS,aAAa,EACnC,IAAI,EACJ,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,WAAW,EACX,WAAW,EACX,YAAY,EAAE,EACI;IAClB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,OAAO,OAAO,EAAE;QAErC,MAAM,MAAM,qLAAA,CAAA,SAAS,CAAC,OAAO,OAAO;QACpC,IAAI,SAAS,CAAC,KAAK,MAAM;QAEzB,2BAA2B;QAC3B,MAAM,OAAO,oMAAA,CAAA,YAAY,CAAC;YAAE,UAAU;QAAK,GACxC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,GACpB,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;QAEhD,wBAAwB;QACxB,MAAM,UAAU,gMAAA,CAAA,UAAU,GACvB,IAAI,CAAC;YAAC;YAAO;SAAO,EACpB,OAAO,CAAC,GACR,KAAK,CAAC;QAET,QAAQ;QAER,eAAe;QACf,MAAM,QAAQ,IACX,SAAS,CAAC,KACV,IAAI,CAAC,KAAK,MAAM,IAChB,KAAK,GACL,MAAM,CAAC,KACP,IAAI,CAAC,aAAa,CAAA,IAAK,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAEtD,iBAAiB;QACjB,MACG,MAAM,CAAC,QACP,IAAI,CAAC,SAAS,CAAA,IAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAC9B,IAAI,CAAC,UAAU,CAAA,IAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAC/B,IAAI,CAAC,QAAQ,CAAA,IAAK,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,IAAI,CAAC,MAAM,GACnD,IAAI,CAAC,UAAU,QACf,IAAI,CAAC,gBAAgB,GACrB,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,WAAW,KACjB,EAAE,CAAC,aAAa,SAAS,KAAK,EAAE,CAAC;YAChC,qLAAA,CAAA,SAAS,CAAC,IAAI,EACX,UAAU,GACV,QAAQ,CAAC,KACT,KAAK,CAAC,WAAW,GACjB,IAAI,CAAC,gBAAgB;YAExB,MAAM,CAAC,GAAG,EAAE,GAAG,uLAAA,CAAA,UAAU,CAAC,OAAO,OAAO,OAAO;YAC/C,WAAW;gBAAE,MAAM,EAAE,IAAI;gBAAE;gBAAG;YAAE;YAChC,cAAc,EAAE,IAAI;QACtB,GACC,EAAE,CAAC,YAAY,SAAS,KAAK,EAAE,CAAC;YAC/B,qLAAA,CAAA,SAAS,CAAC,IAAI,EACX,UAAU,GACV,QAAQ,CAAC,KACT,KAAK,CAAC,WAAW,KACjB,IAAI,CAAC,gBAAgB;YAExB,WAAW;YACX,cAAc;QAChB,GACC,EAAE,CAAC,SAAS,SAAS,KAAK,EAAE,CAAC;YAC5B,cAAc,EAAE,IAAI;QACtB;QAEF,kBAAkB;QAClB,MACG,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,GACV,IAAI,CAAC,KAAK,IACV,KAAK,CAAC,aAAa,CAAA;YAClB,MAAM,YAAY,EAAE,EAAE,GAAG,EAAE,EAAE;YAC7B,MAAM,aAAa,EAAE,EAAE,GAAG,EAAE,EAAE;YAC9B,MAAM,eAAe,KAAK,GAAG,CAAC,WAAW;YACzC,OAAO,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,eAAe,IAAI,EAAE,CAAC;QAC5D,GACC,KAAK,CAAC,eAAe,QACrB,KAAK,CAAC,QAAQ,QACd,KAAK,CAAC,kBAAkB,QACxB,IAAI,CAAC,CAAA;YACJ,MAAM,YAAY,EAAE,EAAE,GAAG,EAAE,EAAE;YAC7B,OAAO,YAAY,KAAK,EAAE,IAAI,CAAC,MAAM,GAAG;QAC1C;QAEF,oCAAoC;QACpC,MACG,MAAM,CAAC,CAAA,IAAK,AAAC,EAAE,EAAE,GAAG,EAAE,EAAE,GAAI,MAAM,AAAC,EAAE,EAAE,GAAG,EAAE,EAAE,GAAI,IAClD,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,GACV,IAAI,CAAC,KAAK,IACV,KAAK,CAAC,aAAa,QACnB,KAAK,CAAC,QAAQ,QACd,KAAK,CAAC,WAAW,KACjB,KAAK,CAAC,kBAAkB,QACxB,IAAI,CAAC,CAAA,IAAK,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,EAAE,IAAI,CAAC,KAAK;QAExC,yCAAyC;QACzC,MACG,MAAM,CAAC,CAAA,IAAK,AAAC,EAAE,EAAE,GAAG,EAAE,EAAE,GAAI,OAAO,AAAC,EAAE,EAAE,GAAG,EAAE,EAAE,GAAI,IACnD,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,GACV,IAAI,CAAC,KAAK,IACV,KAAK,CAAC,aAAa,QACnB,KAAK,CAAC,QAAQ,QACd,KAAK,CAAC,WAAW,KACjB,KAAK,CAAC,kBAAkB,QACxB,IAAI,CAAC,CAAA,IAAK,GAAG,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAE7E,GAAG;QAAC;QAAM;QAAO;QAAQ;QAAa;KAAY;IAElD,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,8OAAC;gBACC,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAU;;;;;;YAGX,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,MAAM,QAAQ,CAAC,GAAG;oBAClB,KAAK,QAAQ,CAAC,GAAG;oBACjB,WAAW,QAAQ,CAAC,GAAG,QAAQ,MAAM,sBAAsB;gBAC7D;;kCAEA,8OAAC;wBAAI,WAAU;kCAAqB,QAAQ,IAAI,CAAC,IAAI;;;;;;kCACrD,8OAAC;wBAAI,WAAU;kCAAyB,QAAQ,IAAI,CAAC,MAAM;;;;;;kCAC3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAI;oCAAQ,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,KAAK;;;;;;;0CAC9C,8OAAC;;oCAAI;oCAAa,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,KAAK;;;;;;;0CACnD,8OAAC;;oCAAI;oCAAS,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,MAAM;;;;;;;0CAChD,8OAAC;gCAAI,WAAW,GAAG,QAAQ,IAAI,CAAC,MAAM,IAAI,IAAI,mBAAmB,gBAAgB;;oCAAE;oCACpE,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,IAAI,CAAC,MAAM;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;oCAAwB;oCAAO,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;AAM3E", "debugId": null}}, {"offset": {"line": 3645, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/ChartContainer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport B<PERSON>ble<PERSON>hart from './BubbleChart';\nimport Treemap<PERSON>hart from './TreemapChart';\nimport { BubbleData, VisualizationMode } from '@/types/crypto';\nimport { BubbleChartSkeleton } from './LoadingSpinner';\n\ninterface ChartContainerProps {\n  data: BubbleData[];\n  mode: VisualizationMode['id'];\n  width?: number;\n  height?: number;\n  isLoading?: boolean;\n  onItemClick?: (data: BubbleData) => void;\n  onItemHover?: (data: BubbleData | null) => void;\n  className?: string;\n}\n\nexport default function ChartContainer({\n  data,\n  mode,\n  width = 800,\n  height = 600,\n  isLoading = false,\n  onItemClick,\n  onItemHover,\n  className = '',\n}: ChartContainerProps) {\n  if (isLoading) {\n    return <BubbleChartSkeleton width={width} height={height} />;\n  }\n\n  const renderChart = () => {\n    switch (mode) {\n      case 'bubble':\n        return (\n          <BubbleChart\n            data={data}\n            width={width}\n            height={height}\n            onBubbleClick={onItemClick}\n            onBubbleHover={onItemHover}\n            className={className}\n          />\n        );\n      \n      case 'treemap':\n        return (\n          <TreemapChart\n            data={data}\n            width={width}\n            height={height}\n            onCellClick={onItemClick}\n            onCellHover={onItemHover}\n            className={className}\n          />\n        );\n      \n      case 'scatter':\n        return (\n          <div \n            className={`border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700 flex items-center justify-center ${className}`}\n            style={{ width, height }}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-gray-500 dark:text-gray-400 mb-2\">\n                <svg className=\"w-16 h-16 mx-auto\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 001.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                Scatter Plot\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                Coming soon! This will show price vs volume relationships.\n              </p>\n            </div>\n          </div>\n        );\n      \n      case 'heatmap':\n        return (\n          <div \n            className={`border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700 flex items-center justify-center ${className}`}\n            style={{ width, height }}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-gray-500 dark:text-gray-400 mb-2\">\n                <svg className=\"w-16 h-16 mx-auto\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                Heatmap\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                Coming soon! This will show price changes across different time periods.\n              </p>\n            </div>\n          </div>\n        );\n      \n      default:\n        return (\n          <BubbleChart\n            data={data}\n            width={width}\n            height={height}\n            onBubbleClick={onItemClick}\n            onBubbleHover={onItemHover}\n            className={className}\n          />\n        );\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      {renderChart()}\n      \n      {/* Mode indicator */}\n      <div className=\"absolute top-4 right-4 bg-white dark:bg-gray-800 px-3 py-1 rounded-full shadow-sm border border-gray-200 dark:border-gray-700\">\n        <span className=\"text-xs font-medium text-gray-600 dark:text-gray-400 capitalize\">\n          {mode} View\n        </span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAmBe,SAAS,eAAe,EACrC,IAAI,EACJ,IAAI,EACJ,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,YAAY,KAAK,EACjB,WAAW,EACX,WAAW,EACX,YAAY,EAAE,EACM;IACpB,IAAI,WAAW;QACb,qBAAO,8OAAC,oIAAA,CAAA,sBAAmB;YAAC,OAAO;YAAO,QAAQ;;;;;;IACpD;IAEA,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,iIAAA,CAAA,UAAW;oBACV,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,eAAe;oBACf,WAAW;;;;;;YAIjB,KAAK;gBACH,qBACE,8OAAC,kIAAA,CAAA,UAAY;oBACX,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,aAAa;oBACb,aAAa;oBACb,WAAW;;;;;;YAIjB,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW,CAAC,oHAAoH,EAAE,WAAW;oBAC7I,OAAO;wBAAE;wBAAO;oBAAO;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAoB,MAAK;oCAAe,SAAQ;8CAC7D,cAAA,8OAAC;wCAAK,UAAS;wCAAU,GAAE;wCAAuS,UAAS;;;;;;;;;;;;;;;;0CAG/U,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;YAOhE,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW,CAAC,oHAAoH,EAAE,WAAW;oBAC7I,OAAO;wBAAE;wBAAO;oBAAO;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAoB,MAAK;oCAAe,SAAQ;8CAC7D,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAGZ,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;YAOhE;gBACE,qBACE,8OAAC,iIAAA,CAAA,UAAW;oBACV,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,eAAe;oBACf,WAAW;;;;;;QAGnB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ;0BAGD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;wBACb;wBAAK;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 3866, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport B<PERSON>ble<PERSON>hart from '@/components/BubbleChart';\nimport LoadingSpinner, { BubbleChartSkeleton } from '@/components/LoadingSpinner';\nimport ErrorDisplay from '@/components/ErrorBoundary';\nimport Header from '@/components/Header';\nimport CryptoModal from '@/components/CryptoModal';\nimport FilterPanel from '@/components/FilterPanel';\nimport VisualizationModeSelector from '@/components/VisualizationModeSelector';\nimport ChartContainer from '@/components/ChartContainer';\nimport { useCryptocurrencies } from '@/hooks/useCrypto';\nimport { BubbleData, FilterOptions, VisualizationMode } from '@/types/crypto';\nimport { filterCryptocurrencies, transformToBubbleData } from '@/utils/dataTransform';\n\nexport default function Home() {\n  const [selectedCrypto, setSelectedCrypto] = useState<BubbleData | null>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [filters, setFilters] = useState<FilterOptions>({});\n  const [visualizationMode, setVisualizationMode] = useState<VisualizationMode['id']>('bubble');\n  const { cryptocurrencies, isLoading, error, mutate } = useCryptocurrencies(100);\n\n  // Apply filters to cryptocurrency data\n  const filteredCryptocurrencies = useMemo(() => {\n    if (!cryptocurrencies) return [];\n    return filterCryptocurrencies(cryptocurrencies, filters);\n  }, [cryptocurrencies, filters]);\n\n  // Transform to bubble data\n  const bubbleData = useMemo(() => {\n    return transformToBubbleData(filteredCryptocurrencies);\n  }, [filteredCryptocurrencies]);\n\n  const handleBubbleClick = (data: BubbleData) => {\n    setSelectedCrypto(data);\n    setIsModalOpen(true);\n  };\n\n  const handleBubbleHover = (data: BubbleData | null) => {\n    // Handle hover events if needed\n  };\n\n  const handleCryptoSearch = (crypto: any) => {\n    // Find the crypto in our data and select it\n    const foundCrypto = bubbleData.find(c => c.id === crypto.id);\n    if (foundCrypto) {\n      setSelectedCrypto(foundCrypto);\n      setIsModalOpen(true);\n    }\n  };\n\n\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <ErrorDisplay\n          error={error}\n          onRetry={() => mutate()}\n          className=\"max-w-md\"\n        />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <Header\n        onRefresh={() => mutate()}\n        isRefreshing={isLoading}\n        onCryptoSearch={handleCryptoSearch}\n      />\n\n        {/* Main Content */}\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n            {/* Main Chart Area */}\n            <div className=\"lg:col-span-3 space-y-6\">\n              {/* Controls Row */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n                {/* Filter Panel */}\n                <FilterPanel\n                  filters={filters}\n                  onFiltersChange={setFilters}\n                />\n\n                {/* Visualization Mode Selector */}\n                <VisualizationModeSelector\n                  currentMode={visualizationMode}\n                  onModeChange={setVisualizationMode}\n                />\n              </div>\n\n              {/* Chart Container */}\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6\">\n                <div className=\"mb-4 flex items-center justify-between\">\n                  <div>\n                    <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      Market Overview\n                    </h2>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {visualizationMode === 'bubble' && 'Bubble size represents market cap, color represents 24h price change'}\n                      {visualizationMode === 'treemap' && 'Rectangle size represents market cap, color represents 24h price change'}\n                      {visualizationMode === 'scatter' && 'X-axis: Volume, Y-axis: Price, color represents 24h price change'}\n                      {visualizationMode === 'heatmap' && 'Grid showing price changes across different time periods'}\n                    </p>\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    Showing {bubbleData.length} cryptocurrencies\n                  </div>\n                </div>\n\n                <ChartContainer\n                  data={bubbleData}\n                  mode={visualizationMode}\n                  width={800}\n                  height={600}\n                  isLoading={isLoading}\n                  onItemClick={handleBubbleClick}\n                  onItemHover={handleBubbleHover}\n                  className=\"w-full\"\n                />\n              </div>\n            </div>\n\n            {/* Sidebar */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                  Market Stats\n                </h3>\n\n                <div className=\"space-y-4\">\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    Click on a bubble to view detailed information about a cryptocurrency.\n                  </p>\n\n                  {!isLoading && bubbleData.length > 0 && (\n                    <div className=\"space-y-2\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-sm text-gray-600 dark:text-gray-400\">Total Coins:</span>\n                        <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {bubbleData.length}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-sm text-gray-600 dark:text-gray-400\">Gainers:</span>\n                        <span className=\"text-sm font-medium text-green-600\">\n                          {bubbleData.filter(d => d.change > 0).length}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-sm text-gray-600 dark:text-gray-400\">Losers:</span>\n                        <span className=\"text-sm font-medium text-red-600\">\n                          {bubbleData.filter(d => d.change < 0).length}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-sm text-gray-600 dark:text-gray-400\">Neutral:</span>\n                        <span className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                          {bubbleData.filter(d => d.change === 0).length}\n                        </span>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n\n        {/* Crypto Details Modal */}\n        <CryptoModal\n          crypto={selectedCrypto}\n          isOpen={isModalOpen}\n          onClose={() => {\n            setIsModalOpen(false);\n            setSelectedCrypto(null);\n          }}\n        />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAbA;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,CAAC;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IACpF,MAAM,EAAE,gBAAgB,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;IAE3E,uCAAuC;IACvC,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvC,IAAI,CAAC,kBAAkB,OAAO,EAAE;QAChC,OAAO,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD,EAAE,kBAAkB;IAClD,GAAG;QAAC;QAAkB;KAAQ;IAE9B,2BAA2B;IAC3B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,OAAO,CAAA,GAAA,6HAAA,CAAA,wBAAqB,AAAD,EAAE;IAC/B,GAAG;QAAC;KAAyB;IAE7B,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;IACzB,gCAAgC;IAClC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,4CAA4C;QAC5C,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;QAC3D,IAAI,aAAa;YACf,kBAAkB;YAClB,eAAe;QACjB;IACF;IAIA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,mIAAA,CAAA,UAAY;gBACX,OAAO;gBACP,SAAS,IAAM;gBACf,WAAU;;;;;;;;;;;IAIlB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,4HAAA,CAAA,UAAM;gBACL,WAAW,IAAM;gBACjB,cAAc;gBACd,gBAAgB;;;;;;0BAIhB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,iIAAA,CAAA,UAAW;4CACV,SAAS;4CACT,iBAAiB;;;;;;sDAInB,8OAAC,+IAAA,CAAA,UAAyB;4CACxB,aAAa;4CACb,cAAc;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAsD;;;;;;sEAGpE,8OAAC;4DAAE,WAAU;;gEACV,sBAAsB,YAAY;gEAClC,sBAAsB,aAAa;gEACnC,sBAAsB,aAAa;gEACnC,sBAAsB,aAAa;;;;;;;;;;;;;8DAGxC,8OAAC;oDAAI,WAAU;;wDAA2C;wDAC/C,WAAW,MAAM;wDAAC;;;;;;;;;;;;;sDAI/B,8OAAC,oIAAA,CAAA,UAAc;4CACb,MAAM;4CACN,MAAM;4CACN,OAAO;4CACP,QAAQ;4CACR,WAAW;4CACX,aAAa;4CACb,aAAa;4CACb,WAAU;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAIzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;4CAIvD,CAAC,aAAa,WAAW,MAAM,GAAG,mBACjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,8OAAC;gEAAK,WAAU;0EACb,WAAW,MAAM;;;;;;;;;;;;kEAGtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,8OAAC;gEAAK,WAAU;0EACb,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,GAAG,MAAM;;;;;;;;;;;;kEAGhD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,8OAAC;gEAAK,WAAU;0EACb,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,GAAG,MAAM;;;;;;;;;;;;kEAGhD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,8OAAC;gEAAK,WAAU;0EACb,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYhE,8OAAC,iIAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,QAAQ;gBACR,SAAS;oBACP,eAAe;oBACf,kBAAkB;gBACpB;;;;;;;;;;;;AAIV", "debugId": null}}]}