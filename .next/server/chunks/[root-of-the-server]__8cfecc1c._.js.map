{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/app/api/crypto/markets/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nconst COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    \n    // Extract query parameters\n    const vs_currency = searchParams.get('vs_currency') || 'usd';\n    const order = searchParams.get('order') || 'market_cap_desc';\n    const per_page = searchParams.get('per_page') || '100';\n    const page = searchParams.get('page') || '1';\n    const sparkline = searchParams.get('sparkline') || 'true';\n    const price_change_percentage = searchParams.get('price_change_percentage') || '1h,24h,7d,30d';\n\n    // Build the CoinGecko API URL\n    const coinGeckoUrl = new URL(`${COINGECKO_API_BASE}/coins/markets`);\n    coinGeckoUrl.searchParams.set('vs_currency', vs_currency);\n    coinGeckoUrl.searchParams.set('order', order);\n    coinGeckoUrl.searchParams.set('per_page', per_page);\n    coinGeckoUrl.searchParams.set('page', page);\n    coinGeckoUrl.searchParams.set('sparkline', sparkline);\n    coinGeckoUrl.searchParams.set('price_change_percentage', price_change_percentage);\n\n    // Make the request to CoinGecko\n    const response = await fetch(coinGeckoUrl.toString(), {\n      headers: {\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      throw new Error(`CoinGecko API error: ${response.status}`);\n    }\n\n    const data = await response.json();\n\n    // Return the data with CORS headers\n    return NextResponse.json(data, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n      },\n    });\n  } catch (error) {\n    console.error('Error fetching cryptocurrency data:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch cryptocurrency data' },\n      { \n        status: 500,\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n        },\n      }\n    );\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,qBAAqB;AAEpB,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,2BAA2B;QAC3B,MAAM,cAAc,aAAa,GAAG,CAAC,kBAAkB;QACvD,MAAM,QAAQ,aAAa,GAAG,CAAC,YAAY;QAC3C,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;QACzC,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QACnD,MAAM,0BAA0B,aAAa,GAAG,CAAC,8BAA8B;QAE/E,8BAA8B;QAC9B,MAAM,eAAe,IAAI,IAAI,GAAG,mBAAmB,cAAc,CAAC;QAClE,aAAa,YAAY,CAAC,GAAG,CAAC,eAAe;QAC7C,aAAa,YAAY,CAAC,GAAG,CAAC,SAAS;QACvC,aAAa,YAAY,CAAC,GAAG,CAAC,YAAY;QAC1C,aAAa,YAAY,CAAC,GAAG,CAAC,QAAQ;QACtC,aAAa,YAAY,CAAC,GAAG,CAAC,aAAa;QAC3C,aAAa,YAAY,CAAC,GAAG,CAAC,2BAA2B;QAEzD,gCAAgC;QAChC,MAAM,WAAW,MAAM,MAAM,aAAa,QAAQ,IAAI;YACpD,SAAS;gBACP,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,MAAM,EAAE;QAC3D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,oCAAoC;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsC,GAC/C;YACE,QAAQ;YACR,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IAEJ;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}